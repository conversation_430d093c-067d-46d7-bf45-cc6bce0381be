# RBAC Implementation Guide for AI Agent

## Overview
This guide provides step-by-step instructions for implementing the enterprise-level RBAC system in the POC LLM AI project. Each section includes code snippets, file modifications, and testing requirements.

## Prerequisites
- Access to the project directory: `/Users/<USER>/Desktop/phananhle/Ekotek/POC/poc-llm-ai/api`
- Understanding of the current codebase structure
- No terminal command execution (all changes via file modifications)

---

## Phase 1: Database Models and Schema

### Step 1.1: Update `api/db/__init__.py`

Add the following RBAC enums to the end of the file:

```python
# RBAC System Enums
class RBACRoleCode(StrEnum):
    # System Level Roles
    SUPER_ADMIN = 'super_admin'
    
    # Department Level Roles
    DEPARTMENT_ADMIN = 'department_admin'
    
    # Tenant Level Roles
    TENANT_MANAGER = 'tenant_manager'
    SENIOR_USER = 'senior_user'
    BASIC_USER = 'basic_user'
    VIEWER = 'viewer'


class RBACRoleLevel(IntEnum):
    SYSTEM = 1
    DEPARTMENT = 2
    TENANT = 3


class ScopeType(StrEnum):
    SYSTEM = 'system'
    DEPARTMENT = 'department'
    TENANT = 'tenant'


class PermissionType(StrEnum):
    # Resource Permissions
    CREATE = 'create'
    READ = 'read'
    UPDATE = 'update'
    DELETE = 'delete'
    MANAGE = 'manage'
    
    # Feature Permissions
    CHAT_ACCESS = 'chat_access'
    ANALYTICS_ACCESS = 'analytics_access'
    API_ACCESS = 'api_access'
    ADMIN_PANEL = 'admin_panel'
    
    # User Management Permissions
    INVITE_USERS = 'invite_users'
    ASSIGN_ROLES = 'assign_roles'
    REMOVE_USERS = 'remove_users'
    
    # System Permissions
    CREATE_DEPARTMENTS = 'create_departments'
    MANAGE_TENANTS = 'manage_tenants'
    GRANT_TENANT_ACCESS = 'grant_tenant_access'


# Default Permission Sets for Each Role
DEFAULT_ROLE_PERMISSIONS = {
    RBACRoleCode.SUPER_ADMIN: {
        'resources': ['create', 'read', 'update', 'delete', 'manage'],
        'features': ['chat_access', 'analytics_access', 'api_access', 'admin_panel'],
        'user_management': ['invite_users', 'assign_roles', 'remove_users'],
        'system': ['create_departments', 'manage_tenants', 'grant_tenant_access']
    },
    RBACRoleCode.DEPARTMENT_ADMIN: {
        'resources': ['create', 'read', 'update', 'delete'],
        'features': ['analytics_access', 'api_access', 'admin_panel'],
        'user_management': ['invite_users', 'assign_roles', 'remove_users'],
        'system': ['manage_tenants']
    },
    RBACRoleCode.TENANT_MANAGER: {
        'resources': ['create', 'read', 'update', 'delete', 'manage'],
        'features': ['analytics_access', 'api_access'],
        'user_management': ['invite_users', 'assign_roles', 'remove_users'],
        'system': []
    },
    RBACRoleCode.SENIOR_USER: {
        'resources': ['create', 'read', 'update'],
        'features': ['analytics_access', 'api_access'],
        'user_management': ['invite_users'],
        'system': []
    },
    RBACRoleCode.BASIC_USER: {
        'resources': ['create', 'read', 'update'],
        'features': ['api_access'],
        'user_management': [],
        'system': []
    },
    RBACRoleCode.VIEWER: {
        'resources': ['read'],
        'features': [],
        'user_management': [],
        'system': []
    }
}

VALID_RBAC_ROLE_CODES = {role for role in RBACRoleCode}
VALID_SCOPE_TYPES = {scope for scope in ScopeType}
VALID_PERMISSION_TYPES = {perm for perm in PermissionType}
```

### Step 1.2: Add New Models to `api/db/db_models.py`

Add these new model classes before the `migrate_db()` function:

```python
class Department(DataBaseModel):
    id = CharField(max_length=32, primary_key=True)
    name = CharField(max_length=255, null=False, help_text="Department name", index=True)
    description = TextField(null=True, help_text="Department description")
    company_id = CharField(max_length=32, null=True, help_text="Company ID for future multi-company support", index=True)
    created_by = CharField(max_length=32, null=False, help_text="User who created this department", index=True)
    status = CharField(max_length=1, null=True, help_text="is it validate(0: wasted, 1: validate)", default="1", index=True)

    class Meta:
        db_table = "department"


class RBACRole(DataBaseModel):
    id = CharField(max_length=32, primary_key=True)
    name = CharField(max_length=100, null=False, help_text="Role display name", index=True)
    code = CharField(max_length=50, null=False, unique=True, help_text="Role code (SUPER_ADMIN, DEPT_ADMIN, etc.)", index=True)
    description = TextField(null=True, help_text="Role description")
    level = IntegerField(null=False, help_text="Role level: 1=System, 2=Department, 3=Tenant", index=True)
    permissions = JSONField(null=False, default={}, help_text="Permission set for this role")
    is_system_role = BooleanField(null=False, default=False, help_text="Is this a built-in system role", index=True)
    status = CharField(max_length=1, null=True, help_text="is it validate(0: wasted, 1: validate)", default="1", index=True)

    class Meta:
        db_table = "rbac_role"


class UserRoleAssignment(DataBaseModel):
    id = CharField(max_length=32, primary_key=True)
    user_id = CharField(max_length=32, null=False, help_text="User ID", index=True)
    role_id = CharField(max_length=32, null=False, help_text="Role ID", index=True)
    scope_type = CharField(max_length=20, null=False, help_text="Scope type: system, department, tenant", index=True)
    scope_id = CharField(max_length=32, null=True, help_text="Department ID or Tenant ID based on scope_type", index=True)
    granted_by = CharField(max_length=32, null=False, help_text="User who granted this role", index=True)
    granted_at = BigIntegerField(null=False, help_text="When the role was granted", index=True)
    expires_at = BigIntegerField(null=True, help_text="When the role expires (null = never)", index=True)
    status = CharField(max_length=1, null=True, help_text="is it validate(0: wasted, 1: validate)", default="1", index=True)

    class Meta:
        db_table = "user_role_assignment"
        indexes = (
            (('user_id', 'role_id', 'scope_type', 'scope_id'), True),  # Unique constraint
        )


class ChatPermission(DataBaseModel):
    id = CharField(max_length=32, primary_key=True)
    user_id = CharField(max_length=32, null=False, help_text="User ID", index=True)
    tenant_id = CharField(max_length=32, null=False, help_text="Tenant ID", index=True)
    granted_by = CharField(max_length=32, null=False, help_text="User who granted chat permission", index=True)
    granted_at = BigIntegerField(null=False, help_text="When permission was granted", index=True)
    expires_at = BigIntegerField(null=True, help_text="When permission expires (null = never)", index=True)
    status = CharField(max_length=1, null=True, help_text="is it validate(0: wasted, 1: validate)", default="1", index=True)

    class Meta:
        db_table = "chat_permission"
        indexes = (
            (('user_id', 'tenant_id'), True),  # Unique constraint
        )
```

### Step 1.3: Update Existing Models in `api/db/db_models.py`

Modify the `User` class by adding this field after the `is_superuser` field:

```python
# Add this field to the User class
is_super_admin = BooleanField(null=True, help_text="Is RBAC Super Admin", default=False, index=True)
```

Modify the `Tenant` class by adding this field after the `name` field:

```python
# Add this field to the Tenant class
department_id = CharField(max_length=32, null=True, help_text="Department this tenant belongs to", index=True)
```

### Step 1.4: Update Migration Function in `api/db/db_models.py`

Add these migration statements to the `migrate_db()` function:

```python
def migrate_db():
    migrator = DatabaseMigrator[settings.DATABASE_TYPE.upper()].value(DB)
    
    # ... existing migration code ...
    
    # RBAC System Migrations
    try:
        migrate(migrator.add_column("user", "is_super_admin", BooleanField(null=True, help_text="Is RBAC Super Admin", default=False, index=True)))
    except Exception:
        pass
    
    try:
        migrate(migrator.add_column("tenant", "department_id", CharField(max_length=32, null=True, help_text="Department this tenant belongs to", index=True)))
    except Exception:
        pass
```

---

## Phase 2: RBAC Service Layer

### Step 2.1: Create `api/db/services/rbac_service.py`

Create this new file:

```python
#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import logging
from datetime import datetime
from typing import List, Dict, Optional, Set

import peewee
from api.db import (
    RBACRoleCode, RBACRoleLevel, ScopeType, PermissionType, 
    DEFAULT_ROLE_PERMISSIONS, StatusEnum
)
from api.db.db_models import (
    DB, Department, RBACRole, UserRoleAssignment, ChatPermission, 
    User, Tenant, UserTenant
)
from api.db.services.common_service import CommonService
from api.utils import get_uuid, current_timestamp


class Permission:
    """Represents a permission with scope and type"""
    
    def __init__(self, permission_type: str, scope_type: str = ScopeType.SYSTEM, scope_id: str = None):
        self.permission_type = permission_type
        self.scope_type = scope_type
        self.scope_id = scope_id
    
    def __str__(self):
        if self.scope_id:
            return f"{self.permission_type}@{self.scope_type}:{self.scope_id}"
        return f"{self.permission_type}@{self.scope_type}"
    
    def __eq__(self, other):
        if not isinstance(other, Permission):
            return False
        return (self.permission_type == other.permission_type and
                self.scope_type == other.scope_type and
                self.scope_id == other.scope_id)
    
    def __hash__(self):
        return hash((self.permission_type, self.scope_type, self.scope_id))


class RBACService(CommonService):
    """Service for managing RBAC roles and permissions"""
    
    model = RBACRole
    
    @classmethod
    @DB.connection_context()
    def create_default_roles(cls):
        """Create default system roles if they don't exist"""
        for role_code, permissions in DEFAULT_ROLE_PERMISSIONS.items():
            if not cls.get_role_by_code(role_code):
                cls.create_role(
                    name=role_code.replace('_', ' ').title(),
                    code=role_code,
                    description=f"Default {role_code.replace('_', ' ').title()} role",
                    level=cls._get_role_level(role_code),
                    permissions=permissions,
                    is_system_role=True
                )
    
    @classmethod
    @DB.connection_context()
    def create_role(cls, name: str, code: str, description: str, level: int, 
                   permissions: Dict, is_system_role: bool = False) -> RBACRole:
        """Create a new role"""
        role_data = {
            'id': get_uuid(),
            'name': name,
            'code': code,
            'description': description,
            'level': level,
            'permissions': permissions,
            'is_system_role': is_system_role,
            'status': StatusEnum.VALID.value
        }
        
        return cls.save(**role_data)
    
    @classmethod
    @DB.connection_context()
    def get_role_by_code(cls, code: str) -> Optional[RBACRole]:
        """Get role by code"""
        try:
            return cls.model.select().where(
                (cls.model.code == code) & 
                (cls.model.status == StatusEnum.VALID.value)
            ).get()
        except peewee.DoesNotExist:
            return None
    
    @classmethod
    @DB.connection_context()
    def get_roles_by_level(cls, level: int) -> List[RBACRole]:
        """Get all roles at a specific level"""
        return list(cls.model.select().where(
            (cls.model.level == level) & 
            (cls.model.status == StatusEnum.VALID.value)
        ))
    
    @classmethod
    def _get_role_level(cls, role_code: str) -> int:
        """Get the level for a role code"""
        if role_code == RBACRoleCode.SUPER_ADMIN:
            return RBACRoleLevel.SYSTEM
        elif role_code == RBACRoleCode.DEPARTMENT_ADMIN:
            return RBACRoleLevel.DEPARTMENT
        else:
            return RBACRoleLevel.TENANT


class UserRoleService(CommonService):
    """Service for managing user role assignments"""
    
    model = UserRoleAssignment
    
    @classmethod
    @DB.connection_context()
    def assign_role(cls, user_id: str, role_id: str, scope_type: str, 
                   scope_id: str = None, granted_by: str = None, expires_at: int = None) -> UserRoleAssignment:
        """Assign a role to a user with specific scope"""
        
        # Check if assignment already exists
        existing = cls.get_user_role_assignment(user_id, role_id, scope_type, scope_id)
        if existing:
            raise ValueError("Role assignment already exists")
        
        assignment_data = {
            'id': get_uuid(),
            'user_id': user_id,
            'role_id': role_id,
            'scope_type': scope_type,
            'scope_id': scope_id,
            'granted_by': granted_by,
            'granted_at': current_timestamp(),
            'expires_at': expires_at,
            'status': StatusEnum.VALID.value
        }
        
        return cls.save(**assignment_data)
    
    @classmethod
    @DB.connection_context()
    def get_user_role_assignment(cls, user_id: str, role_id: str, scope_type: str, scope_id: str = None) -> Optional[UserRoleAssignment]:
        """Get a specific user role assignment"""
        try:
            query = cls.model.select().where(
                (cls.model.user_id == user_id) &
                (cls.model.role_id == role_id) &
                (cls.model.scope_type == scope_type) &
                (cls.model.status == StatusEnum.VALID.value)
            )
            
            if scope_id:
                query = query.where(cls.model.scope_id == scope_id)
            else:
                query = query.where(cls.model.scope_id.is_null())
            
            return query.get()
        except peewee.DoesNotExist:
            return None
    
    @classmethod
    @DB.connection_context()
    def get_user_roles(cls, user_id: str) -> List[Dict]:
        """Get all active roles for a user with role details"""
        current_time = current_timestamp()
        
        query = (cls.model
                .select(cls.model, RBACRole)
                .join(RBACRole, on=(cls.model.role_id == RBACRole.id))
                .where(
                    (cls.model.user_id == user_id) &
                    (cls.model.status == StatusEnum.VALID.value) &
                    (RBACRole.status == StatusEnum.VALID.value) &
                    ((cls.model.expires_at.is_null()) | (cls.model.expires_at > current_time))
                ))
        
        roles = []
        for assignment in query:
            roles.append({
                'assignment_id': assignment.id,
                'role_id': assignment.role_id,
                'role_code': assignment.rbac_role.code,
                'role_name': assignment.rbac_role.name,
                'scope_type': assignment.scope_type,
                'scope_id': assignment.scope_id,
                'permissions': assignment.rbac_role.permissions,
                'granted_at': assignment.granted_at,
                'expires_at': assignment.expires_at
            })
        
        return roles
    
    @classmethod
    @DB.connection_context()
    def revoke_role(cls, assignment_id: str) -> bool:
        """Revoke a role assignment"""
        try:
            cls.model.update({'status': StatusEnum.INVALID.value}).where(
                cls.model.id == assignment_id
            ).execute()
            return True
        except Exception as e:
            logging.error(f"Failed to revoke role assignment {assignment_id}: {e}")
            return False
    
    @classmethod
    @DB.connection_context()
    def get_users_with_role(cls, role_code: str, scope_type: str = None, scope_id: str = None) -> List[Dict]:
        """Get all users with a specific role"""
        query = (User
                .select(User, cls.model, RBACRole)
                .join(cls.model, on=(User.id == cls.model.user_id))
                .join(RBACRole, on=(cls.model.role_id == RBACRole.id))
                .where(
                    (RBACRole.code == role_code) &
                    (cls.model.status == StatusEnum.VALID.value) &
                    (RBACRole.status == StatusEnum.VALID.value) &
                    (User.status == StatusEnum.VALID.value)
                ))
        
        if scope_type:
            query = query.where(cls.model.scope_type == scope_type)
        
        if scope_id:
            query = query.where(cls.model.scope_id == scope_id)
        
        users = []
        for user in query:
            users.append({
                'user_id': user.id,
                'email': user.email,
                'nickname': user.nickname,
                'assignment_id': user.user_role_assignment.id,
                'scope_type': user.user_role_assignment.scope_type,
                'scope_id': user.user_role_assignment.scope_id,
                'granted_at': user.user_role_assignment.granted_at
            })
        
        return users


class DepartmentService(CommonService):
    """Service for managing departments"""
    
    model = Department
    
    @classmethod
    @DB.connection_context()
    def create_department(cls, name: str, description: str = None, created_by: str = None) -> Department:
        """Create a new department"""
        department_data = {
            'id': get_uuid(),
            'name': name,
            'description': description,
            'created_by': created_by,
            'status': StatusEnum.VALID.value
        }
        
        return cls.save(**department_data)
    
    @classmethod
    @DB.connection_context()
    def get_department_tenants(cls, department_id: str) -> List[Tenant]:
        """Get all tenants in a department"""
        return list(Tenant.select().where(
            (Tenant.department_id == department_id) &
            (Tenant.status == StatusEnum.VALID.value)
        ))
    
    @classmethod
    @DB.connection_context()
    def get_user_departments(cls, user_id: str) -> List[Dict]:
        """Get departments where user has department-level roles"""
        assignments = UserRoleService.get_user_roles(user_id)
        department_roles = [a for a in assignments if a['scope_type'] == ScopeType.DEPARTMENT]
        
        departments = []
        for role in department_roles:
            try:
                dept = cls.model.get_by_id(role['scope_id'])
                departments.append({
                    'department_id': dept.id,
                    'department_name': dept.name,
                    'role_code': role['role_code'],
                    'role_name': role['role_name']
                })
            except peewee.DoesNotExist:
                continue
        
        return departments


class ChatPermissionService(CommonService):
    """Service for managing chat permissions"""
    
    model = ChatPermission
    
    @classmethod
    @DB.connection_context()
    def grant_chat_permission(cls, user_id: str, tenant_id: str, granted_by: str, expires_at: int = None) -> ChatPermission:
        """Grant chat permission to a user for a tenant"""
        
        # Check if permission already exists
        existing = cls.get_chat_permission(user_id, tenant_id)
        if existing:
            raise ValueError("Chat permission already exists")
        
        permission_data = {
            'id': get_uuid(),
            'user_id': user_id,
            'tenant_id': tenant_id,
            'granted_by': granted_by,
            'granted_at': current_timestamp(),
            'expires_at': expires_at,
            'status': StatusEnum.VALID.value
        }
        
        return cls.save(**permission_data)
    
    @classmethod
    @DB.connection_context()
    def get_chat_permission(cls, user_id: str, tenant_id: str) -> Optional[ChatPermission]:
        """Get chat permission for user and tenant"""
        current_time = current_timestamp()
        
        try:
            return cls.model.select().where(
                (cls.model.user_id == user_id) &
                (cls.model.tenant_id == tenant_id) &
                (cls.model.status == StatusEnum.VALID.value) &
                ((cls.model.expires_at.is_null()) | (cls.model.expires_at > current_time))
            ).get()
        except peewee.DoesNotExist:
            return None
    
    @classmethod
    @DB.connection_context()
    def revoke_chat_permission(cls, user_id: str, tenant_id: str) -> bool:
        """Revoke chat permission"""
        try:
            cls.model.update({'status': StatusEnum.INVALID.value}).where(
                (cls.model.user_id == user_id) &
                (cls.model.tenant_id == tenant_id)
            ).execute()
            return True
        except Exception as e:
            logging.error(f"Failed to revoke chat permission for user {user_id} in tenant {tenant_id}: {e}")
            return False
    
    @classmethod
    @DB.connection_context()
    def user_has_chat_access(cls, user_id: str, tenant_id: str) -> bool:
        """Check if user has chat access for tenant"""
        return cls.get_chat_permission(user_id, tenant_id) is not None
    
    @classmethod
    @DB.connection_context()
    def get_user_chat_permissions(cls, user_id: str) -> List[Dict]:
        """Get all chat permissions for a user"""
        current_time = current_timestamp()
        
        query = (cls.model
                .select(cls.model, Tenant)
                .join(Tenant, on=(cls.model.tenant_id == Tenant.id))
                .where(
                    (cls.model.user_id == user_id) &
                    (cls.model.status == StatusEnum.VALID.value) &
                    (Tenant.status == StatusEnum.VALID.value) &
                    ((cls.model.expires_at.is_null()) | (cls.model.expires_at > current_time))
                ))
        
        permissions = []
        for perm in query:
            permissions.append({
                'permission_id': perm.id,
                'tenant_id': perm.tenant_id,
                'tenant_name': perm.tenant.name,
                'granted_at': perm.granted_at,
                'expires_at': perm.expires_at
            })
        
        return permissions
```

### Step 2.2: Create `api/utils/rbac_utils.py`

Create this new file:

```python
#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import logging
from functools import wraps
from typing import List, Dict, Set, Optional
from flask import request
from flask_login import current_user

from api.db import RBACRoleCode, ScopeType, StatusEnum
from api.db.services.rbac_service import (
    RBACService, UserRoleService, ChatPermissionService, Permission
)
from api.utils.api_utils import get_json_result
from api import settings


class RBACPermissionChecker:
    """Utility class for checking RBAC permissions"""
    
    @staticmethod
    def user_has_permission(user_id: str, permission_type: str, scope_type: str = ScopeType.SYSTEM, scope_id: str = None) -> bool:
        """Check if user has a specific permission"""
        user_roles = UserRoleService.get_user_roles(user_id)
        
        for role in user_roles:
            # Check if role matches the scope
            if role['scope_type'] != scope_type:
                continue
            
            if scope_id and role['scope_id'] != scope_id:
                continue
            
            # Check if role has the permission
            permissions = role.get('permissions', {})
            if RBACPermissionChecker._role_has_permission(permissions, permission_type):
                return True
        
        return False
    
    @staticmethod
    def _role_has_permission(permissions: Dict, permission_type: str) -> bool:
        """Check if a role's permission set includes the permission type"""
        for category, perms in permissions.items():
            if permission_type in perms:
                return True
        return False
    
    @staticmethod
    def user_has_role(user_id: str, role_code: str, scope_type: str = None, scope_id: str = None) -> bool:
        """Check if user has a specific role"""
        user_roles = UserRoleService.get_user_roles(user_id)
        
        for role in user_roles:
            if role['role_code'] != role_code:
                continue
            
            if scope_type and role['scope_type'] != scope_type:
                continue
            
            if scope_id and role['scope_id'] != scope_id:
                continue
            
            return True
        
        return False
    
    @staticmethod
    def user_is_super_admin(user_id: str) -> bool:
        """Check if user is Super Admin"""
        return RBACPermissionChecker.user_has_role(user_id, RBACRoleCode.SUPER_ADMIN, ScopeType.SYSTEM)
    
    @staticmethod
    def user_is_department_admin(user_id: str, department_id: str = None) -> bool:
        """Check if user is Department Admin"""
        return RBACPermissionChecker.user_has_role(
            user_id, RBACRoleCode.DEPARTMENT_ADMIN, ScopeType.DEPARTMENT, department_id
        )
    
    @staticmethod
    def user_is_tenant_manager(user_id: str, tenant_id: str = None) -> bool:
        """Check if user is Tenant Manager"""
        return RBACPermissionChecker.user_has_role(
            user_id, RBACRoleCode.TENANT_MANAGER, ScopeType.TENANT, tenant_id
        )
    
    @staticmethod
    def user_can_grant_chat_access(user_id: str, tenant_id: str) -> bool:
        """Check if user can grant chat access for a tenant"""
        return RBACPermissionChecker.user_is_tenant_manager(user_id, tenant_id)
    
    @staticmethod
    def get_user_accessible_tenants(user_id: str) -> List[str]:
        """Get list of tenant IDs user has access to"""
        user_roles = UserRoleService.get_user_roles(user_id)
        tenant_ids = set()
        
        for role in user_roles:
            if role['scope_type'] == ScopeType.TENANT and role['scope_id']:
                tenant_ids.add(role['scope_id'])
            elif role['scope_type'] == ScopeType.DEPARTMENT and role['scope_id']:
                # Department admins have access to all tenants in their department
                from api.db.services.rbac_service import DepartmentService
                dept_tenants = DepartmentService.get_department_tenants(role['scope_id'])
                tenant_ids.update([t.id for t in dept_tenants])
        
        return list(tenant_ids)


# Permission Decorators
def require_permission(permission_type: str, scope_type: str = ScopeType.SYSTEM, scope_id_param: str = None):
    """Decorator to require specific permission for an endpoint"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return get_json_result(
                    data=False,
                    message='Authentication required.',
                    code=settings.RetCode.AUTHENTICATION_ERROR
                )
            
            # Get scope_id from request parameters if specified
            scope_id = None
            if scope_id_param:
                scope_id = kwargs.get(scope_id_param) or request.json.get(scope_id_param) if request.json else None
            
            # Check permission
            if not RBACPermissionChecker.user_has_permission(
                current_user.id, permission_type, scope_type, scope_id
            ):
                return get_json_result(
                    data=False,
                    message=f'Permission denied. Required: {permission_type} in {scope_type}',
                    code=settings.RetCode.AUTHENTICATION_ERROR
                )
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def require_role(role_code: str, scope_type: str = ScopeType.SYSTEM, scope_id_param: str = None):
    """Decorator to require specific role for an endpoint"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return get_json_result(
                    data=False,
                    message='Authentication required.',
                    code=settings.RetCode.AUTHENTICATION_ERROR
                )
            
            # Get scope_id from request parameters if specified
            scope_id = None
            if scope_id_param:
                scope_id = kwargs.get(scope_id_param) or request.json.get(scope_id_param) if request.json else None
            
            # Check role
            if not RBACPermissionChecker.user_has_role(
                current_user.id, role_code, scope_type, scope_id
            ):
                return get_json_result(
                    data=False,
                    message=f'Role required: {role_code} in {scope_type}',
                    code=settings.RetCode.AUTHENTICATION_ERROR
                )
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def require_chat_permission(tenant_id_param: str = 'tenant_id'):
    """Decorator to require chat permission for a tenant"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return get_json_result(
                    data=False,
                    message='Authentication required.',
                    code=settings.RetCode.AUTHENTICATION_ERROR
                )
            
            # Get tenant_id from request parameters
            tenant_id = kwargs.get(tenant_id_param) or request.json.get(tenant_id_param) if request.json else None
            
            if not tenant_id:
                return get_json_result(
                    data=False,
                    message='Tenant ID required for chat access.',
                    code=settings.RetCode.ARGUMENT_ERROR
                )
            
            # Check chat permission
            if not ChatPermissionService.user_has_chat_access(current_user.id, tenant_id):
                return get_json_result(
                    data=False,
                    message='Chat access denied for this tenant.',
                    code=settings.RetCode.AUTHENTICATION_ERROR
                )
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def require_super_admin(f):
    """Decorator to require Super Admin role"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return get_json_result(
                data=False,
                message='Authentication required.',
                code=settings.RetCode.AUTHENTICATION_ERROR
            )
        
        if not RBACPermissionChecker.user_is_super_admin(current_user.id):
            return get_json_result(
                data=False,
                message='Super Admin access required.',
                code=settings.RetCode.AUTHENTICATION_ERROR
            )
        
        return f(*args, **kwargs)
    return decorated_function


# Utility Functions
def current_user_has_permission(permission_type: str, scope_type: str = ScopeType.SYSTEM, scope_id: str = None) -> bool:
    """Check if current authenticated user has permission"""
    if not current_user.is_authenticated:
        return False
    
    return RBACPermissionChecker.user_has_permission(
        current_user.id, permission_type, scope_type, scope_id
    )


def current_user_has_role(role_code: str, scope_type: str = None, scope_id: str = None) -> bool:
    """Check if current authenticated user has role"""
    if not current_user.is_authenticated:
        return False
    
    return RBACPermissionChecker.user_has_role(
        current_user.id, role_code, scope_type, scope_id
    )


def current_user_has_chat_access(tenant_id: str) -> bool:
    """Check if current authenticated user has chat access for tenant"""
    if not current_user.is_authenticated:
        return False
    
    return ChatPermissionService.user_has_chat_access(current_user.id, tenant_id)


def get_current_user_roles() -> List[Dict]:
    """Get all roles for current authenticated user"""
    if not current_user.is_authenticated:
        return []
    
    return UserRoleService.get_user_roles(current_user.id)


def get_current_user_accessible_tenants() -> List[str]:
    """Get tenants accessible by current user"""
    if not current_user.is_authenticated:
        return []
    
    return RBACPermissionChecker.get_user_accessible_tenants(current_user.id)
```

---

## Phase 3: Super Admin Initialization

### Step 3.1: Create `api/db/init_rbac.py`

Create this new file:

```python
#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import logging
import secrets
import os
from datetime import datetime
from werkzeug.security import generate_password_hash

from api.db import RBACRoleCode, ScopeType, StatusEnum
from api.db.db_models import DB, User
from api.db.services.rbac_service import RBACService, UserRoleService
from api.utils import get_uuid, current_timestamp, datetime_format


def init_super_admin():
    """Initialize Super Admin account if none exists"""
    try:
        with DB.connection_context():
            # Check if Super Admin already exists
            existing_super_admins = UserRoleService.get_users_with_role(
                RBACRoleCode.SUPER_ADMIN, ScopeType.SYSTEM
            )
            
            if existing_super_admins:
                logging.info(f"Super Admin already exists: {existing_super_admins[0]['email']}")
                return existing_super_admins[0]
            
            # Check if user with is_super_admin flag exists
            try:
                existing_user = User.select().where(
                    (User.is_super_admin == True) & 
                    (User.status == StatusEnum.VALID.value)
                ).get()
                
                logging.info(f"Found existing Super Admin user: {existing_user.email}")
                
                # Assign Super Admin role to existing user
                super_admin_role = RBACService.get_role_by_code(RBACRoleCode.SUPER_ADMIN)
                if super_admin_role:
                    UserRoleService.assign_role(
                        user_id=existing_user.id,
                        role_id=super_admin_role.id,
                        scope_type=ScopeType.SYSTEM,
                        granted_by=existing_user.id  # Self-granted for initial setup
                    )
                
                return {
                    'user_id': existing_user.id,
                    'email': existing_user.email,
                    'existing': True
                }
                
            except User.DoesNotExist:
                pass
            
            # Create new Super Admin account
            super_admin_email = os.getenv('RBAC_SUPER_ADMIN_EMAIL', '<EMAIL>')
            super_admin_password = os.getenv('RBAC_SUPER_ADMIN_PASSWORD', secrets.token_urlsafe(16))
            
            # Create Super Admin user
            super_admin_data = {
                'id': get_uuid(),
                'email': super_admin_email,
                'nickname': 'Super Admin',
                'password': generate_password_hash(super_admin_password),
                'is_super_admin': True,
                'is_superuser': True,  # Keep existing superuser flag
                'is_authenticated': '1',
                'is_active': '1',
                'is_anonymous': '0',
                'status': StatusEnum.VALID.value,
                'access_token': get_uuid(),
                'create_time': current_timestamp(),
                'create_date': datetime_format(datetime.now()),
                'update_time': current_timestamp(),
                'update_date': datetime_format(datetime.now())
            }
            
            user = User(**super_admin_data)
            user.save(force_insert=True)
            
            # Assign Super Admin role
            super_admin_role = RBACService.get_role_by_code(RBACRoleCode.SUPER_ADMIN)
            if super_admin_role:
                UserRoleService.assign_role(
                    user_id=user.id,
                    role_id=super_admin_role.id,
                    scope_type=ScopeType.SYSTEM,
                    granted_by=user.id  # Self-granted for initial setup
                )
            
            logging.warning(
                f"RBAC Super Admin created:\n"
                f"Email: {super_admin_email}\n"
                f"Password: {super_admin_password}\n"
                f"IMPORTANT: Change this password immediately!"
            )
            
            return {
                'user_id': user.id,
                'email': super_admin_email,
                'password': super_admin_password,
                'created': True
            }
            
    except Exception as e:
        logging.error(f"Failed to initialize Super Admin: {e}")
        raise


def init_rbac_system():
    """Initialize the complete RBAC system"""
    try:
        with DB.connection_context():
            logging.info("Initializing RBAC system...")
            
            # 1. Create default roles
            logging.info("Creating default RBAC roles...")
            RBACService.create_default_roles()
            
            # 2. Initialize Super Admin
            logging.info("Initializing Super Admin...")
            super_admin_info = init_super_admin()
            
            # 3. Log completion
            logging.info("RBAC system initialization completed successfully")
            
            return {
                'success': True,
                'super_admin': super_admin_info,
                'message': 'RBAC system initialized successfully'
            }
            
    except Exception as e:
        logging.error(f"RBAC system initialization failed: {e}")
        return {
            'success': False,
            'error': str(e),
            'message': 'RBAC system initialization failed'
        }


def migrate_existing_users():
    """Migrate existing users to RBAC system (optional)"""
    try:
        with DB.connection_context():
            logging.info("Starting user migration to RBAC system...")
            
            # Get all existing users without RBAC roles
            users_to_migrate = []
            all_users = User.select().where(User.status == StatusEnum.VALID.value)
            
            for user in all_users:
                user_roles = UserRoleService.get_user_roles(user.id)
                if not user_roles and not user.is_super_admin:
                    users_to_migrate.append(user)
            
            if not users_to_migrate:
                logging.info("No users need migration")
                return {'migrated': 0, 'message': 'No users to migrate'}
            
            # Assign basic user role to users without roles
            basic_user_role = RBACService.get_role_by_code(RBACRoleCode.BASIC_USER)
            if not basic_user_role:
                raise Exception("Basic User role not found")
            
            migrated_count = 0
            for user in users_to_migrate:
                try:
                    # Assign to system scope initially - Super Admin can reassign later
                    UserRoleService.assign_role(
                        user_id=user.id,
                        role_id=basic_user_role.id,
                        scope_type=ScopeType.SYSTEM,
                        granted_by=None  # System migration
                    )
                    migrated_count += 1
                except Exception as e:
                    logging.warning(f"Failed to migrate user {user.email}: {e}")
            
            logging.info(f"Migrated {migrated_count} users to RBAC system")
            return {
                'migrated': migrated_count,
                'total': len(users_to_migrate),
                'message': f'Migrated {migrated_count} users successfully'
            }
            
    except Exception as e:
        logging.error(f"User migration failed: {e}")
        return {
            'migrated': 0,
            'error': str(e),
            'message': 'User migration failed'
        }
```

### Step 3.2: Update `api/db/db_models.py` to call RBAC initialization

Add this import at the top of the file:

```python
# Add this import with other imports
from api.db.init_rbac import init_rbac_system
```

Modify the `init_database_tables()` function to call RBAC initialization:

```python
@DB.connection_context()
def init_database_tables(alter_fields=[]):
    # ... existing code ...
    
    if create_failed_list:
        logging.error(f"create tables failed: {create_failed_list}")
        raise Exception(f"create tables failed: {create_failed_list}")
    
    migrate_db()
    
    # Initialize RBAC system after database setup
    try:
        from api.db.init_rbac import init_rbac_system
        rbac_result = init_rbac_system()
        if rbac_result['success']:
            logging.info("RBAC system initialized successfully")
        else:
            logging.error(f"RBAC initialization failed: {rbac_result['message']}")
    except Exception as e:
        logging.error(f"Failed to initialize RBAC system: {e}")
```

---

## Phase 4: API Endpoints

### Step 4.1: Create `api/apps/rbac_app.py`

Create this new file:

```python
#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

from flask import request
from flask_login import login_required, current_user

from api import settings
from api.db import RBACRoleCode, ScopeType, StatusEnum
from api.db.services.rbac_service import (
    RBACService, UserRoleService, DepartmentService, ChatPermissionService
)
from api.db.services.user_service import UserService
from api.utils.api_utils import get_json_result, validate_request, server_error_response, get_data_error_result
from api.utils.rbac_utils import (
    require_super_admin, require_role, require_permission,
    RBACPermissionChecker, current_user_has_role
)


# Department Management
@manager.route('/departments', methods=['POST'])  # noqa: F821
@login_required
@require_super_admin
@validate_request("name")
def create_department():
    """Create a new department (Super Admin only)"""
    try:
        req = request.json
        department = DepartmentService.create_department(
            name=req['name'],
            description=req.get('description'),
            created_by=current_user.id
        )
        
        return get_json_result(data=department.to_dict())
    except Exception as e:
        return server_error_response(e)


@manager.route('/departments', methods=['GET'])  # noqa: F821
@login_required
@require_permission('admin_panel')
def list_departments():
    """List all departments"""
    try:
        departments = DepartmentService.query(status=StatusEnum.VALID.value)
        return get_json_result(data=[dept.to_dict() for dept in departments])
    except Exception as e:
        return server_error_response(e)


@manager.route('/departments/<department_id>', methods=['PUT'])  # noqa: F821
@login_required
@require_role(RBACRoleCode.DEPARTMENT_ADMIN, ScopeType.DEPARTMENT, 'department_id')
@validate_request("name")
def update_department(department_id):
    """Update department (Department Admin only)"""
    try:
        req = request.json
        DepartmentService.update_by_id(department_id, req)
        
        department = DepartmentService.filter_by_id(department_id)
        return get_json_result(data=department.to_dict() if department else None)
    except Exception as e:
        return server_error_response(e)


@manager.route('/departments/<department_id>', methods=['DELETE'])  # noqa: F821
@login_required
@require_super_admin
def delete_department(department_id):
    """Delete department (Super Admin only)"""
    try:
        DepartmentService.update_by_id(department_id, {'status': StatusEnum.INVALID.value})
        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)


# Role Management
@manager.route('/roles', methods=['GET'])  # noqa: F821
@login_required
@require_permission('admin_panel')
def list_roles():
    """List all available roles"""
    try:
        roles = RBACService.query(status=StatusEnum.VALID.value)
        return get_json_result(data=[role.to_dict() for role in roles])
    except Exception as e:
        return server_error_response(e)


@manager.route('/roles/custom', methods=['POST'])  # noqa: F821
@login_required
@require_super_admin
@validate_request("name", "code", "level", "permissions")
def create_custom_role():
    """Create custom role (Super Admin only)"""
    try:
        req = request.json
        role = RBACService.create_role(
            name=req['name'],
            code=req['code'],
            description=req.get('description', ''),
            level=req['level'],
            permissions=req['permissions'],
            is_system_role=False
        )
        
        return get_json_result(data=role.to_dict())
    except Exception as e:
        return server_error_response(e)


# User Role Assignments
@manager.route('/assignments', methods=['POST'])  # noqa: F821
@login_required
@validate_request("user_id", "role_code", "scope_type")
def assign_role_to_user():
    """Assign role to user"""
    try:
        req = request.json
        user_id = req['user_id']
        role_code = req['role_code']
        scope_type = req['scope_type']
        scope_id = req.get('scope_id')
        expires_at = req.get('expires_at')
        
        # Permission checks based on role being assigned
        if role_code == RBACRoleCode.SUPER_ADMIN:
            if not RBACPermissionChecker.user_is_super_admin(current_user.id):
                return get_json_result(
                    data=False,
                    message='Only Super Admin can assign Super Admin role.',
                    code=settings.RetCode.AUTHENTICATION_ERROR
                )
        
        elif role_code == RBACRoleCode.DEPARTMENT_ADMIN:
            if not (RBACPermissionChecker.user_is_super_admin(current_user.id) or
                   RBACPermissionChecker.user_is_department_admin(current_user.id, scope_id)):
                return get_json_result(
                    data=False,
                    message='Only Super Admin or Department Admin can assign Department Admin role.',
                    code=settings.RetCode.AUTHENTICATION_ERROR
                )
        
        else:  # Tenant-level roles
            if not (RBACPermissionChecker.user_is_super_admin(current_user.id) or
                   RBACPermissionChecker.user_is_department_admin(current_user.id) or
                   RBACPermissionChecker.user_is_tenant_manager(current_user.id, scope_id)):
                return get_json_result(
                    data=False,
                    message='Insufficient permissions to assign this role.',
                    code=settings.RetCode.AUTHENTICATION_ERROR
                )
        
        # Get role by code
        role = RBACService.get_role_by_code(role_code)
        if not role:
            return get_data_error_result(message=f"Role not found: {role_code}")
        
        # Assign role
        assignment = UserRoleService.assign_role(
            user_id=user_id,
            role_id=role.id,
            scope_type=scope_type,
            scope_id=scope_id,
            granted_by=current_user.id,
            expires_at=expires_at
        )
        
        return get_json_result(data=assignment.to_dict())
        
    except ValueError as e:
        return get_data_error_result(message=str(e))
    except Exception as e:
        return server_error_response(e)


@manager.route('/assignments/<assignment_id>', methods=['DELETE'])  # noqa: F821
@login_required
def revoke_role_assignment(assignment_id):
    """Revoke a role assignment"""
    try:
        # Get assignment details for permission check
        assignment = UserRoleService.filter_by_id(assignment_id)
        if not assignment:
            return get_data_error_result(message="Assignment not found")
        
        # Permission checks - only higher-level roles can revoke
        if not (RBACPermissionChecker.user_is_super_admin(current_user.id) or
               (assignment.scope_type == ScopeType.DEPARTMENT and 
                RBACPermissionChecker.user_is_department_admin(current_user.id, assignment.scope_id)) or
               (assignment.scope_type == ScopeType.TENANT and 
                RBACPermissionChecker.user_is_tenant_manager(current_user.id, assignment.scope_id))):
            return get_json_result(
                data=False,
                message='Insufficient permissions to revoke this role.',
                code=settings.RetCode.AUTHENTICATION_ERROR
            )
        
        success = UserRoleService.revoke_role(assignment_id)
        return get_json_result(data=success)
        
    except Exception as e:
        return server_error_response(e)


@manager.route('/users/<user_id>/roles', methods=['GET'])  # noqa: F821
@login_required
def get_user_roles(user_id):
    """Get all roles for a user"""
    try:
        # Permission check - can view own roles or if admin
        if (user_id != current_user.id and 
            not current_user_has_role(RBACRoleCode.SUPER_ADMIN) and
            not current_user_has_role(RBACRoleCode.DEPARTMENT_ADMIN)):
            return get_json_result(
                data=False,
                message='Permission denied.',
                code=settings.RetCode.AUTHENTICATION_ERROR
            )
        
        roles = UserRoleService.get_user_roles(user_id)
        return get_json_result(data=roles)
        
    except Exception as e:
        return server_error_response(e)


# Chat Permissions
@manager.route('/chat-permissions', methods=['POST'])  # noqa: F821
@login_required
@validate_request("user_id", "tenant_id")
def grant_chat_permission():
    """Grant chat permission to user for tenant"""
    try:
        req = request.json
        user_id = req['user_id']
        tenant_id = req['tenant_id']
        expires_at = req.get('expires_at')
        
        # Only Tenant Manager can grant chat permissions
        if not RBACPermissionChecker.user_can_grant_chat_access(current_user.id, tenant_id):
            return get_json_result(
                data=False,
                message='Only Tenant Manager can grant chat permissions.',
                code=settings.RetCode.AUTHENTICATION_ERROR
            )
        
        permission = ChatPermissionService.grant_chat_permission(
            user_id=user_id,
            tenant_id=tenant_id,
            granted_by=current_user.id,
            expires_at=expires_at
        )
        
        return get_json_result(data=permission.to_dict())
        
    except ValueError as e:
        return get_data_error_result(message=str(e))
    except Exception as e:
        return server_error_response(e)


@manager.route('/chat-permissions', methods=['GET'])  # noqa: F821
@login_required
def list_chat_permissions():
    """List chat permissions (filtered by user access)"""
    try:
        user_id = request.args.get('user_id')
        tenant_id = request.args.get('tenant_id')
        
        if user_id and user_id != current_user.id:
            # Only admins can view other users' permissions
            if not (current_user_has_role(RBACRoleCode.SUPER_ADMIN) or
                   current_user_has_role(RBACRoleCode.DEPARTMENT_ADMIN) or
                   (tenant_id and RBACPermissionChecker.user_is_tenant_manager(current_user.id, tenant_id))):
                return get_json_result(
                    data=False,
                    message='Permission denied.',
                    code=settings.RetCode.AUTHENTICATION_ERROR
                )
            
            permissions = ChatPermissionService.get_user_chat_permissions(user_id)
        else:
            # Get current user's permissions
            permissions = ChatPermissionService.get_user_chat_permissions(current_user.id)
        
        return get_json_result(data=permissions)
        
    except Exception as e:
        return server_error_response(e)


@manager.route('/chat-permissions/<user_id>/<tenant_id>', methods=['DELETE'])  # noqa: F821
@login_required
def revoke_chat_permission(user_id, tenant_id):
    """Revoke chat permission"""
    try:
        # Only Tenant Manager can revoke chat permissions
        if not RBACPermissionChecker.user_can_grant_chat_access(current_user.id, tenant_id):
            return get_json_result(
                data=False,
                message='Only Tenant Manager can revoke chat permissions.',
                code=settings.RetCode.AUTHENTICATION_ERROR
            )
        
        success = ChatPermissionService.revoke_chat_permission(user_id, tenant_id)
        return get_json_result(data=success)
        
    except Exception as e:
        return server_error_response(e)


# Utility Endpoints
@manager.route('/permissions/check', methods=['POST'])  # noqa: F821
@login_required
@validate_request("permission_type")
def check_permission():
    """Check if current user has specific permission"""
    try:
        req = request.json
        permission_type = req['permission_type']
        scope_type = req.get('scope_type', ScopeType.SYSTEM)
        scope_id = req.get('scope_id')
        
        has_permission = RBACPermissionChecker.user_has_permission(
            current_user.id, permission_type, scope_type, scope_id
        )
        
        return get_json_result(data={'has_permission': has_permission})
        
    except Exception as e:
        return server_error_response(e)


@manager.route('/users/accessible-tenants', methods=['GET'])  # noqa: F821
@login_required
def get_accessible_tenants():
    """Get tenants accessible by current user"""
    try:
        tenant_ids = RBACPermissionChecker.get_user_accessible_tenants(current_user.id)
        return get_json_result(data={'tenant_ids': tenant_ids})
        
    except Exception as e:
        return server_error_response(e)


@manager.route('/users/pending-approval', methods=['GET'])  # noqa: F821
@login_required
@require_super_admin
def get_pending_users():
    """Get users pending tenant approval (Super Admin only)"""
    try:
        # Get users without any role assignments
        all_users = UserService.query(status=StatusEnum.VALID.value)
        pending_users = []
        
        for user in all_users:
            if not user.is_super_admin:
                user_roles = UserRoleService.get_user_roles(user.id)
                if not user_roles:
                    pending_users.append({
                        'user_id': user.id,
                        'email': user.email,
                        'nickname': user.nickname,
                        'create_date': user.create_date
                    })
        
        return get_json_result(data=pending_users)
        
    except Exception as e:
        return server_error_response(e)


@manager.route('/tenants/<tenant_id>/grant-access', methods=['POST'])  # noqa: F821
@login_required
@require_super_admin
@validate_request("user_id", "role_code")
def grant_tenant_access(tenant_id):
    """Grant tenant access to user (Super Admin only)"""
    try:
        req = request.json
        user_id = req['user_id']
        role_code = req['role_code']
        
        # Get role by code
        role = RBACService.get_role_by_code(role_code)
        if not role:
            return get_data_error_result(message=f"Role not found: {role_code}")
        
        # Assign role to user for this tenant
        assignment = UserRoleService.assign_role(
            user_id=user_id,
            role_id=role.id,
            scope_type=ScopeType.TENANT,
            scope_id=tenant_id,
            granted_by=current_user.id
        )
        
        return get_json_result(data=assignment.to_dict())
        
    except Exception as e:
        return server_error_response(e)
```

### Step 4.2: Create `api/apps/department_app.py`

Create this new file:

```python
#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

from flask import request
from flask_login import login_required, current_user

from api import settings
from api.db import RBACRoleCode, ScopeType, StatusEnum
from api.db.services.rbac_service import DepartmentService, UserRoleService
from api.db.services.user_service import TenantService
from api.utils.api_utils import get_json_result, validate_request, server_error_response
from api.utils.rbac_utils import require_role, RBACPermissionChecker


@manager.route('/<department_id>/tenants', methods=['GET'])  # noqa: F821
@login_required
@require_role(RBACRoleCode.DEPARTMENT_ADMIN, ScopeType.DEPARTMENT, 'department_id')
def get_department_tenants(department_id):
    """Get all tenants in department (Department Admin only)"""
    try:
        tenants = DepartmentService.get_department_tenants(department_id)
        return get_json_result(data=[tenant.to_dict() for tenant in tenants])
    except Exception as e:
        return server_error_response(e)


@manager.route('/<department_id>/users', methods=['GET'])  # noqa: F821
@login_required
@require_role(RBACRoleCode.DEPARTMENT_ADMIN, ScopeType.DEPARTMENT, 'department_id')
def get_department_users(department_id):
    """Get all users with roles in department (Department Admin only)"""
    try:
        dept_users = UserRoleService.get_users_with_role(
            role_code=None,  # Get all roles
            scope_type=ScopeType.DEPARTMENT,
            scope_id=department_id
        )
        
        # Also get users in department tenants
        tenant_users = []
        tenants = DepartmentService.get_department_tenants(department_id)
        for tenant in tenants:
            tenant_users.extend(UserRoleService.get_users_with_role(
                role_code=None,
                scope_type=ScopeType.TENANT,
                scope_id=tenant.id
            ))
        
        # Combine and deduplicate
        all_users = {user['user_id']: user for user in dept_users + tenant_users}
        
        return get_json_result(data=list(all_users.values()))
    except Exception as e:
        return server_error_response(e)


@manager.route('/<department_id>/tenants', methods=['POST'])  # noqa: F821
@login_required
@require_role(RBACRoleCode.DEPARTMENT_ADMIN, ScopeType.DEPARTMENT, 'department_id')
@validate_request("name")
def create_department_tenant(department_id):
    """Create tenant in department (Department Admin only)"""
    try:
        req = request.json
        
        # Create tenant with department reference
        tenant_data = {
            'name': req['name'],
            'department_id': department_id,
            'created_by': current_user.id,
            'llm_id': req.get('llm_id', 'default'),
            'embd_id': req.get('embd_id', 'default'),
            'asr_id': req.get('asr_id', 'default'),
            'img2txt_id': req.get('img2txt_id', 'default'),
            'rerank_id': req.get('rerank_id', 'default'),
            'parser_ids': req.get('parser_ids', 'naive')
        }
        
        tenant = TenantService.save(**tenant_data)
        return get_json_result(data=tenant.to_dict())
        
    except Exception as e:
        return server_error_response(e)
```

---

## Phase 5: Update Existing Applications

### Step 5.1: Update `api/apps/user_app.py`

Find the user registration function and modify it to remove automatic tenant assignment:

```python
# Find the user registration function (around line 100-150) and replace with:

@manager.route("/register", methods=["POST"])  # noqa: F821
def register():
    """
    User registration endpoint - Modified for RBAC
    Users are registered but no tenant access is granted by default
    """
    if not request.json:
        return get_json_result(data=False, code=settings.RetCode.ARGUMENT_ERROR, message="Invalid request!")

    email = request.json.get("email", "")
    if UserService.query(email=email):
        return get_json_result(
            data=False,
            code=settings.RetCode.DUPLICATE_ERROR,
            message=f"Email: {email} has already been registered!",
        )

    password = request.json.get("password")
    try:
        password = decrypt(password)
    except BaseException:
        return get_json_result(data=False, code=settings.RetCode.SERVER_ERROR, message="Fail to decrypt password")

    nickname = request.json.get("nickname", email.split("@")[0])
    
    # Create user without tenant access
    user_data = {
        "email": email,
        "nickname": nickname,
        "password": password,
        "status": StatusEnum.VALID.value,
        "is_authenticated": "1",
        "is_active": "1",
        "is_anonymous": "0",
        "access_token": get_uuid(),
        "avatar": request.json.get("avatar", "")
    }
    
    try:
        user_id = UserService.save(**user_data)
        
        # Log successful registration
        logging.info(f"User registered successfully: {email}. Awaiting Super Admin approval for tenant access.")
        
        return get_json_result(
            data={
                "user_id": user_id,
                "email": email,
                "nickname": nickname,
                "message": "Registration successful! Please wait for administrator approval to access tenants."
            }
        )
        
    except Exception as e:
        return server_error_response(e)
```

### Step 5.2: Update `api/apps/tenant_app.py`

Add RBAC permission checks to tenant operations:

```python
# Add these imports at the top of the file
from api.utils.rbac_utils import (
    require_permission, require_role, RBACPermissionChecker,
    current_user_has_role
)
from api.db import RBACRoleCode, ScopeType

# Update the user_list function
@manager.route("/<tenant_id>/user/list", methods=["GET"])  # noqa: F821
@login_required
@require_role(RBACRoleCode.TENANT_MANAGER, ScopeType.TENANT, 'tenant_id')
def user_list(tenant_id):
    """List users in tenant (Tenant Manager only)"""
    try:
        # Get users with roles in this tenant
        tenant_users = UserRoleService.get_users_with_role(
            role_code=None,
            scope_type=ScopeType.TENANT,
            scope_id=tenant_id
        )
        
        return get_json_result(data=tenant_users)
    except Exception as e:
        return server_error_response(e)

# Update the create function
@manager.route('/<tenant_id>/user', methods=['POST'])  # noqa: F821
@login_required
@require_role(RBACRoleCode.TENANT_MANAGER, ScopeType.TENANT, 'tenant_id')
@validate_request("email", "role_code")
def invite_user_to_tenant(tenant_id):
    """Invite user to tenant with specific role (Tenant Manager only)"""
    try:
        req = request.json
        invite_user_email = req["email"]
        role_code = req["role_code"]
        
        # Find user by email
        invite_users = UserService.query(email=invite_user_email)
        if not invite_users:
            return get_data_error_result(message="User not found.")

        user_id_to_invite = invite_users[0].id
        
        # Check if user already has role in this tenant
        existing_roles = UserRoleService.get_user_roles(user_id_to_invite)
        for role in existing_roles:
            if role['scope_type'] == ScopeType.TENANT and role['scope_id'] == tenant_id:
                return get_data_error_result(
                    message=f"{invite_user_email} already has role {role['role_code']} in this tenant."
                )
        
        # Get role by code
        role = RBACService.get_role_by_code(role_code)
        if not role:
            return get_data_error_result(message=f"Role not found: {role_code}")
        
        # Assign role
        assignment = UserRoleService.assign_role(
            user_id=user_id_to_invite,
            role_id=role.id,
            scope_type=ScopeType.TENANT,
            scope_id=tenant_id,
            granted_by=current_user.id
        )
        
        return get_json_result(data={
            'assignment_id': assignment.id,
            'user_email': invite_user_email,
            'role_code': role_code,
            'tenant_id': tenant_id
        })
        
    except Exception as e:
        return server_error_response(e)
```

### Step 5.3: Update Chat-Related Endpoints

Find chat/dialog endpoints and add chat permission checks:

```python
# Add to relevant chat endpoints in api/apps/dialog_app.py

from api.utils.rbac_utils import require_chat_permission

@manager.route("/<dialog_id>/completion", methods=["POST"])  # noqa: F821
@login_required
@require_chat_permission('tenant_id')  # tenant_id should be in request
def chat_completion(dialog_id):
    """Chat completion with RBAC chat permission check"""
    # ... existing chat logic ...
```

---

## Phase 6: Frontend Integration Points

### Step 6.1: Create `api/apps/rbac_ui_app.py`

Create this new file for UI-specific RBAC endpoints:

```python
#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

from flask import request
from flask_login import login_required, current_user

from api.db.services.rbac_service import UserRoleService, ChatPermissionService
from api.utils.api_utils import get_json_result, server_error_response
from api.utils.rbac_utils import get_current_user_roles, get_current_user_accessible_tenants


@manager.route('/user/profile', methods=['GET'])  # noqa: F821
@login_required
def get_user_rbac_profile():
    """Get current user's RBAC profile for UI"""
    try:
        user_roles = get_current_user_roles()
        accessible_tenants = get_current_user_accessible_tenants()
        chat_permissions = ChatPermissionService.get_user_chat_permissions(current_user.id)
        
        # Organize roles by scope for UI
        system_roles = [r for r in user_roles if r['scope_type'] == 'system']
        department_roles = [r for r in user_roles if r['scope_type'] == 'department']
        tenant_roles = [r for r in user_roles if r['scope_type'] == 'tenant']
        
        profile = {
            'user_id': current_user.id,
            'email': current_user.email,
            'nickname': current_user.nickname,
            'roles': {
                'system': system_roles,
                'department': department_roles,
                'tenant': tenant_roles
            },
            'accessible_tenants': accessible_tenants,
            'chat_permissions': chat_permissions,
            'permissions': {
                'is_super_admin': any(r['role_code'] == 'super_admin' for r in system_roles),
                'is_department_admin': len(department_roles) > 0,
                'can_create_departments': any(r['role_code'] == 'super_admin' for r in system_roles),
                'can_manage_users': len(system_roles + department_roles) > 0
            }
        }
        
        return get_json_result(data=profile)
        
    except Exception as e:
        return server_error_response(e)


@manager.route('/navigation/menu', methods=['GET'])  # noqa: F821
@login_required
def get_navigation_menu():
    """Get navigation menu items based on user permissions"""
    try:
        user_roles = get_current_user_roles()
        
        # Build menu based on roles
        menu_items = {
            'dashboard': True,  # Everyone gets dashboard
            'tenants': len([r for r in user_roles if r['scope_type'] == 'tenant']) > 0,
            'departments': any(r['role_code'] in ['super_admin', 'department_admin'] for r in user_roles),
            'user_management': any(r['role_code'] == 'super_admin' for r in user_roles),
            'role_management': any(r['role_code'] == 'super_admin' for r in user_roles),
            'chat_permissions': any(r['role_code'] in ['tenant_manager'] for r in user_roles),
            'analytics': any('analytics_access' in str(r.get('permissions', {})) for r in user_roles),
            'admin_panel': any('admin_panel' in str(r.get('permissions', {})) for r in user_roles)
        }
        
        return get_json_result(data=menu_items)
        
    except Exception as e:
        return server_error_response(e)
```

---

## Phase 7: Testing and Validation

### Step 7.1: Create `tests/test_rbac.py`

Create this new test file:

```python
#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import unittest
from unittest.mock import patch, MagicMock

from api.db import RBACRoleCode, ScopeType, StatusEnum
from api.db.services.rbac_service import (
    RBACService, UserRoleService, DepartmentService, ChatPermissionService
)
from api.utils.rbac_utils import RBACPermissionChecker
from api.db.init_rbac import init_rbac_system


class TestRBACSystem(unittest.TestCase):
    """Test cases for RBAC system"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_user_id = "test_user_123"
        self.test_tenant_id = "test_tenant_123"
        self.test_department_id = "test_dept_123"
    
    def test_role_creation(self):
        """Test role creation and retrieval"""
        # Test creating default roles
        RBACService.create_default_roles()
        
        # Test retrieving roles
        super_admin_role = RBACService.get_role_by_code(RBACRoleCode.SUPER_ADMIN)
        self.assertIsNotNone(super_admin_role)
        self.assertEqual(super_admin_role.code, RBACRoleCode.SUPER_ADMIN)
    
    def test_permission_checking(self):
        """Test permission checking logic"""
        # Create test role assignment
        role = RBACService.get_role_by_code(RBACRoleCode.TENANT_MANAGER)
        if role:
            UserRoleService.assign_role(
                user_id=self.test_user_id,
                role_id=role.id,
                scope_type=ScopeType.TENANT,
                scope_id=self.test_tenant_id,
                granted_by=self.test_user_id
            )
        
        # Test permission check
        has_permission = RBACPermissionChecker.user_has_permission(
            self.test_user_id, 'create', ScopeType.TENANT, self.test_tenant_id
        )
        self.assertTrue(has_permission)
    
    def test_chat_permissions(self):
        """Test chat permission system"""
        # Grant chat permission
        permission = ChatPermissionService.grant_chat_permission(
            user_id=self.test_user_id,
            tenant_id=self.test_tenant_id,
            granted_by=self.test_user_id
        )
        self.assertIsNotNone(permission)
        
        # Check chat access
        has_access = ChatPermissionService.user_has_chat_access(
            self.test_user_id, self.test_tenant_id
        )
        self.assertTrue(has_access)
        
        # Revoke permission
        revoked = ChatPermissionService.revoke_chat_permission(
            self.test_user_id, self.test_tenant_id
        )
        self.assertTrue(revoked)
    
    def test_rbac_initialization(self):
        """Test RBAC system initialization"""
        result = init_rbac_system()
        self.assertTrue(result['success'])
        self.assertIn('super_admin', result)


if __name__ == '__main__':
    unittest.main()
```

---

## Phase 8: Configuration and Deployment

### Step 8.1: Environment Variables

Add these environment variables to your application:

```bash
# RBAC Configuration
RBAC_SUPER_ADMIN_EMAIL=<EMAIL>
RBAC_SUPER_ADMIN_PASSWORD=secure_password_here

# Optional: Enable RBAC features
RBAC_ENABLED=true
RBAC_AUTO_MIGRATE=true
```

### Step 8.2: Update Application Startup

Modify your main application file to include RBAC initialization:

```python
# In your main app startup file (likely api/ragflow_server.py)

from api.db.init_rbac import init_rbac_system

def create_app():
    # ... existing app creation code ...
    
    # Initialize RBAC system on startup
    try:
        rbac_result = init_rbac_system()
        if rbac_result['success']:
            app.logger.info("RBAC system initialized successfully")
            if 'created' in rbac_result['super_admin']:
                app.logger.warning(f"Super Admin created: {rbac_result['super_admin']}")
        else:
            app.logger.error(f"RBAC initialization failed: {rbac_result}")
    except Exception as e:
        app.logger.error(f"Failed to initialize RBAC: {e}")
    
    return app
```

---

## Implementation Checklist

### Phase 1: Database Schema ✓
- [ ] Add RBAC enums to `api/db/__init__.py`
- [ ] Add new models to `api/db/db_models.py`
- [ ] Update existing models (User, Tenant)
- [ ] Add migration functions
- [ ] Test database creation and migrations

### Phase 2: RBAC Services ✓
- [ ] Create `api/db/services/rbac_service.py`
- [ ] Create `api/utils/rbac_utils.py`
- [ ] Implement permission checking logic
- [ ] Create role management services
- [ ] Test service layer functions

### Phase 3: Super Admin Init ✓
- [ ] Create `api/db/init_rbac.py`
- [ ] Implement auto-initialization
- [ ] Add to application startup
- [ ] Test Super Admin creation
- [ ] Verify default roles creation

### Phase 4: API Endpoints ✓
- [ ] Create `api/apps/rbac_app.py`
- [ ] Create `api/apps/department_app.py`
- [ ] Implement all RBAC endpoints
- [ ] Add permission decorators
- [ ] Test API functionality

### Phase 5: Update Existing Apps ✓
- [ ] Modify user registration flow
- [ ] Update tenant management
- [ ] Add permission checks to existing endpoints
- [ ] Update chat/dialog features
- [ ] Test backward compatibility

### Phase 6: UI Integration ✓
- [ ] Create `api/apps/rbac_ui_app.py`
- [ ] Implement user profile endpoints
- [ ] Create navigation menu logic
- [ ] Test UI integration points

### Phase 7: Testing ✓
- [ ] Create comprehensive test suite
- [ ] Test all RBAC functionality
- [ ] Perform security testing
- [ ] Test migration procedures
- [ ] Validate performance

### Phase 8: Deployment ✓
- [ ] Configure environment variables
- [ ] Update application startup
- [ ] Test in staging environment
- [ ] Create deployment documentation
- [ ] Plan production rollout

## Post-Implementation Notes

1. **Security Review**: Conduct thorough security review before production deployment
2. **Performance Testing**: Test with realistic user loads and data volumes
3. **Documentation**: Update all user and administrator documentation
4. **Training**: Train administrators on new RBAC system
5. **Monitoring**: Set up monitoring and alerting for RBAC operations
6. **Backup**: Ensure RBAC data is included in backup procedures

## Troubleshooting

### Common Issues:
1. **Migration Failures**: Check database permissions and connection
2. **Permission Denied**: Verify role assignments and scope settings
3. **Super Admin Creation**: Check environment variables and logs
4. **Performance Issues**: Review database indexes and query optimization

### Debug Tools:
- Check RBAC logs in application logs
- Use permission checking endpoints for debugging
- Review role assignments in database
- Monitor API response times for RBAC operations

This implementation provides a comprehensive, enterprise-ready RBAC system that meets all the specified requirements while maintaining scalability and security.

---

## ✅ IMPLEMENTATION CHECKLIST - COMPLETED

### Phase 1: Database Models and Schema ✅
- [x] Update `api/db/__init__.py` with RBAC enums
- [x] Add new models to `api/db/db_models.py`
- [x] Update existing User and Tenant models
- [x] Add migration statements to `migrate_db()` function

### Phase 2: RBAC Service Layer ✅
- [x] Create `api/db/services/rbac_service.py`
- [x] Implement RBACService class
- [x] Implement UserRoleService class
- [x] Implement DepartmentService class
- [x] Implement ChatPermissionService class

### Phase 3: Super Admin Initialization ✅
- [x] Create `api/db/init_rbac.py`
- [x] Implement init_super_admin() function
- [x] Implement init_rbac_system() function
- [x] Update database initialization to call RBAC setup

### Phase 4: API Endpoints ✅
- [x] Create `api/apps/rbac_app.py`
- [x] Implement department management endpoints
- [x] Implement role management endpoints
- [x] Implement user role assignment endpoints
- [x] Implement chat permission endpoints
- [x] Implement permission checking endpoints

### Phase 5: Utility Layer ✅
- [x] Create `api/utils/rbac_utils.py`
- [x] Implement RBACPermissionChecker class
- [x] Create security decorators
- [x] Implement helper functions

### Phase 6: Testing and Documentation ✅
- [x] Create comprehensive test suite
- [x] Create implementation documentation
- [x] Create API documentation
- [x] Create deployment guide

---

## 🚀 NEXT STEPS FOR PRODUCTION DEPLOYMENT

### Immediate Actions Required

1. **Test the Implementation**
   ```bash
   # Run the comprehensive test suite
   python test_rbac_system.py
   ```

2. **Configure Environment (Optional)**
   ```bash
   # Set custom Super Admin credentials
   export RBAC_SUPER_ADMIN_EMAIL="<EMAIL>"
   export RBAC_SUPER_ADMIN_PASSWORD="your_secure_password"
   ```

3. **Start the Service**
   ```bash
   # Use the project's standard startup command - RBAC will auto-initialize
   make run-be-service

   # Monitor the service startup and RBAC initialization
   make attach-be
   ```

### Integration Steps

4. **Update Existing Endpoints**
   Add RBAC decorators to existing API endpoints:
   ```python
   from api.utils.rbac_utils import require_permission, require_chat_permission

   @require_permission('create', ScopeType.TENANT, 'tenant_id')
   def create_document(tenant_id):
       # Existing logic
       pass

   @require_chat_permission('tenant_id')
   def chat_endpoint(tenant_id):
       # Existing chat logic
       pass
   ```

5. **Frontend Integration**
   - Update UI to check user permissions before showing actions
   - Integrate with RBAC API endpoints
   - Implement role-based navigation

6. **Create Initial Organization Structure**
   ```bash
   # Use the API to create departments and assign roles
   curl -X POST /v1/rbac/departments \
     -H "Authorization: Bearer <super_admin_token>" \
     -d '{"name": "Engineering", "description": "Engineering Department"}'
   ```

### Production Readiness

7. **Security Review**
   - [ ] Review all permission assignments
   - [ ] Validate role hierarchy
   - [ ] Test permission inheritance
   - [ ] Verify audit trail functionality

8. **Performance Testing**
   - [ ] Test with 1000+ users
   - [ ] Benchmark permission checking performance
   - [ ] Validate database query efficiency
   - [ ] Test concurrent role assignments

9. **Monitoring Setup**
   - [ ] Set up RBAC operation logging
   - [ ] Monitor permission check latency
   - [ ] Alert on failed role assignments
   - [ ] Track Super Admin activities

10. **Documentation and Training**
    - [ ] Create user guides for each role type
    - [ ] Train administrators on RBAC management
    - [ ] Document emergency procedures
    - [ ] Create troubleshooting guides

---

## 🔄 MIGRATION AND STARTUP BEHAVIOR

### **Yes, migrations run automatically on every startup!**

The RBAC system is designed to be **zero-configuration** and **migration-safe**:

#### Automatic Migration Behavior:
1. **Every Service Restart**: The `migrate_db()` function runs on every startup
2. **Safe Migrations**: All migration statements use `try/except` blocks
3. **Idempotent Operations**: Migrations can run multiple times safely
4. **No Data Loss**: Existing data is preserved during migrations

#### What Happens on Startup:
```python
# In api/db/db_models.py - init_database_tables()
def init_database_tables():
    # 1. Create any missing tables
    # 2. Run all migrations (including RBAC)
    migrate_db()

    # 3. Initialize RBAC system
    from api.db.init_rbac import init_rbac_system
    rbac_result = init_rbac_system()
```

#### RBAC Initialization on Startup:
1. **First Startup**:
   - Creates RBAC tables
   - Creates default roles
   - Creates Super Admin account
   - Logs credentials (if auto-generated)

2. **Subsequent Startups**:
   - Checks if RBAC tables exist (skips creation)
   - Checks if default roles exist (skips creation)
   - Checks if Super Admin exists (skips creation)
   - Logs "RBAC system already initialized"

#### Migration Safety Features:
```python
# Example migration pattern used
try:
    migrate(migrator.add_column("user", "is_super_admin", BooleanField(...)))
except Exception:
    pass  # Column already exists, skip
```

### Production Deployment Workflow:

1. **Deploy Code**: Deploy the new RBAC-enabled code
2. **Start Service**: Use `make run-be-service` - migrations run automatically
3. **Monitor Startup**: Use `make attach-be` to view initialization logs
4. **Verify Setup**: Check logs for successful RBAC initialization
5. **Test Access**: Verify Super Admin can access RBAC endpoints
6. **Configure Organization**: Create departments and assign roles

### Rollback Safety:
- **Database**: RBAC tables are additive (no existing data modified)
- **Code**: Old endpoints continue to work (RBAC is additive security)
- **Users**: Existing users retain access (migration assigns basic roles)

The system is designed for **zero-downtime deployment** with automatic, safe migrations that preserve all existing functionality while adding enterprise RBAC capabilities.
