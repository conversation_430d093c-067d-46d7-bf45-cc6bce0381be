# RAGFlow Enterprise Multi-Tenant System Design Strategy

## Executive Summary

This document outlines a comprehensive strategy for transforming RAGFlow from its current single-tenant architecture into an enterprise-level multi-tenant system with department-based organization, granular role-based access control (RBAC), and secure inter-department document sharing capabilities.

## Current Architecture Analysis

### Existing Components
- **User Management**: Basic user registration and authentication
- **Tenant System**: Simple 1:1 user-tenant relationship with basic roles (OWNER, ADMIN, NORMAL, INVITE)
- **Knowledge Base**: Tenant-scoped document repositories
- **Authentication**: Flask-Login with JWT tokens and OAuth/OIDC support
- **Authorization**: Basic tenant-level access control

### Current Limitations
1. **Single-tenant per user**: Each user creates their own tenant ("Kingdom")
2. **Limited role hierarchy**: Only 4 basic roles without granular permissions
3. **No department structure**: No organizational hierarchy within tenants
4. **Basic sharing**: Limited collaboration features between users
5. **Flat permission model**: No fine-grained document-level permissions

## Enterprise Multi-Tenant Architecture Design

### 1. Database Schema Modifications

#### New Tables

```sql
-- Companies table for enterprise organizations
CREATE TABLE companies (
    id VARCHAR(32) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE, -- for email domain-based auto-assignment
    subscription_tier VARCHAR(50) DEFAULT 'enterprise',
    max_users INT DEFAULT 1000,
    max_departments INT DEFAULT 50,
    settings JSON,
    status CHAR(1) DEFAULT '1',
    created_by VARCHAR(32),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_date DATE,
    update_date DATE,
    INDEX idx_domain (domain),
    INDEX idx_status (status)
);

-- Departments within companies
CREATE TABLE departments (
    id VARCHAR(32) PRIMARY KEY,
    company_id VARCHAR(32) NOT NULL,
    parent_department_id VARCHAR(32), -- for hierarchical departments
    name VARCHAR(255) NOT NULL,
    description TEXT,
    department_code VARCHAR(50), -- unique within company
    settings JSON,
    status CHAR(1) DEFAULT '1',
    created_by VARCHAR(32),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_date DATE,
    update_date DATE,
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (parent_department_id) REFERENCES departments(id),
    INDEX idx_company_id (company_id),
    INDEX idx_parent_department_id (parent_department_id),
    INDEX idx_status (status),
    UNIQUE KEY unique_dept_code_per_company (company_id, department_code)
);

-- Enhanced user-company-department relationships
CREATE TABLE user_company_departments (
    id VARCHAR(32) PRIMARY KEY,
    user_id VARCHAR(32) NOT NULL,
    company_id VARCHAR(32) NOT NULL,
    department_id VARCHAR(32) NOT NULL,
    role VARCHAR(50) NOT NULL, -- COMPANY_ADMIN, DEPT_ADMIN, DEPT_MEMBER, GUEST
    permissions JSON, -- granular permissions override
    invited_by VARCHAR(32),
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status CHAR(1) DEFAULT '1',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(id),
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (department_id) REFERENCES departments(id),
    INDEX idx_user_id (user_id),
    INDEX idx_company_id (company_id),
    INDEX idx_department_id (department_id),
    INDEX idx_role (role),
    UNIQUE KEY unique_user_dept (user_id, department_id)
);

-- Document sharing between departments
CREATE TABLE document_shares (
    id VARCHAR(32) PRIMARY KEY,
    document_id VARCHAR(32) NOT NULL,
    shared_by_user_id VARCHAR(32) NOT NULL,
    shared_by_department_id VARCHAR(32) NOT NULL,
    shared_with_department_id VARCHAR(32) NOT NULL,
    permission_level VARCHAR(20) NOT NULL, -- READ_ONLY, READ_WRITE, ADMIN
    expires_at TIMESTAMP NULL,
    shared_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status CHAR(1) DEFAULT '1',
    FOREIGN KEY (document_id) REFERENCES document(id),
    FOREIGN KEY (shared_by_user_id) REFERENCES user(id),
    FOREIGN KEY (shared_by_department_id) REFERENCES departments(id),
    FOREIGN KEY (shared_with_department_id) REFERENCES departments(id),
    INDEX idx_document_id (document_id),
    INDEX idx_shared_with_dept (shared_with_department_id),
    INDEX idx_expires_at (expires_at)
);

-- Audit trail for document access and sharing
CREATE TABLE document_audit_log (
    id VARCHAR(32) PRIMARY KEY,
    document_id VARCHAR(32) NOT NULL,
    user_id VARCHAR(32) NOT NULL,
    department_id VARCHAR(32) NOT NULL,
    action VARCHAR(50) NOT NULL, -- VIEW, EDIT, SHARE, UNSHARE, DELETE
    details JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES document(id),
    FOREIGN KEY (user_id) REFERENCES user(id),
    FOREIGN KEY (department_id) REFERENCES departments(id),
    INDEX idx_document_id (document_id),
    INDEX idx_user_id (user_id),
    INDEX idx_timestamp (timestamp)
);
```

#### Modified Tables

```sql
-- Modify knowledgebase table to support department ownership
ALTER TABLE knowledgebase
ADD COLUMN company_id VARCHAR(32), 
ADD COLUMN department_id VARCHAR(32),
ADD COLUMN visibility VARCHAR(20) DEFAULT 'DEPARTMENT', -- DEPARTMENT, COMPANY, SHARED
ADD FOREIGN KEY (company_id) REFERENCES companies(id),
ADD FOREIGN KEY (department_id) REFERENCES departments(id),
ADD INDEX idx_company_id (company_id),
ADD INDEX idx_department_id (department_id);

-- Modify document table for enhanced access control
ALTER TABLE document
ADD COLUMN company_id VARCHAR(32),
ADD COLUMN department_id VARCHAR(32),
ADD COLUMN access_level VARCHAR(20) DEFAULT 'DEPARTMENT', -- DEPARTMENT, COMPANY, SHARED
ADD COLUMN permissions JSON, -- fine-grained permissions
ADD FOREIGN KEY (company_id) REFERENCES companies(id),
ADD FOREIGN KEY (department_id) REFERENCES departments(id),
ADD INDEX idx_company_id (company_id),
ADD INDEX idx_department_id (department_id);
```

### 2. Enhanced Role-Based Access Control (RBAC)

#### Role Hierarchy

```python
class EnterpriseRole(StrEnum):
    # Company-level roles
    COMPANY_ADMIN = 'company_admin'      # Full access across all departments
    COMPANY_VIEWER = 'company_viewer'    # Read-only access across all departments
    
    # Department-level roles  
    DEPT_ADMIN = 'dept_admin'           # Full access within department
    DEPT_MEMBER = 'dept_member'         # Read/write access to department documents
    DEPT_VIEWER = 'dept_viewer'         # Read-only access to department documents
    
    # Special roles
    GUEST = 'guest'                     # Limited access to specific shared documents
    SYSTEM_ADMIN = 'system_admin'       # Platform administration (super user)

class Permission(StrEnum):
    # Document permissions
    DOC_CREATE = 'doc_create'
    DOC_READ = 'doc_read'
    DOC_UPDATE = 'doc_update'
    DOC_DELETE = 'doc_delete'
    DOC_SHARE = 'doc_share'
    
    # Knowledge base permissions
    KB_CREATE = 'kb_create'
    KB_READ = 'kb_read'
    KB_UPDATE = 'kb_update'
    KB_DELETE = 'kb_delete'
    KB_MANAGE = 'kb_manage'
    
    # Department permissions
    DEPT_MANAGE_USERS = 'dept_manage_users'
    DEPT_MANAGE_SETTINGS = 'dept_manage_settings'
    DEPT_VIEW_ANALYTICS = 'dept_view_analytics'
    
    # Company permissions
    COMPANY_MANAGE_DEPTS = 'company_manage_depts'
    COMPANY_MANAGE_USERS = 'company_manage_users'
    COMPANY_VIEW_ALL = 'company_view_all'

# Role-Permission mapping
ROLE_PERMISSIONS = {
    EnterpriseRole.COMPANY_ADMIN: [
        Permission.DOC_CREATE, Permission.DOC_READ, Permission.DOC_UPDATE,
        Permission.DOC_DELETE, Permission.DOC_SHARE,
        Permission.KB_CREATE, Permission.KB_READ, Permission.KB_UPDATE,
        Permission.KB_DELETE, Permission.KB_MANAGE,
        Permission.DEPT_MANAGE_USERS, Permission.DEPT_MANAGE_SETTINGS, 
        Permission.DEPT_VIEW_ANALYTICS,
        Permission.COMPANY_MANAGE_DEPTS, Permission.COMPANY_MANAGE_USERS, 
        Permission.COMPANY_VIEW_ALL
    ],
    EnterpriseRole.DEPT_ADMIN: [
        Permission.DOC_CREATE, Permission.DOC_READ, Permission.DOC_UPDATE, 
        Permission.DOC_DELETE, Permission.DOC_SHARE,
        Permission.KB_CREATE, Permission.KB_READ, Permission.KB_UPDATE, 
        Permission.KB_DELETE, Permission.KB_MANAGE,
        Permission.DEPT_MANAGE_USERS, Permission.DEPT_MANAGE_SETTINGS, 
        Permission.DEPT_VIEW_ANALYTICS
    ],
    EnterpriseRole.DEPT_MEMBER: [
        Permission.DOC_CREATE, Permission.DOC_READ, Permission.DOC_UPDATE, 
        Permission.DOC_SHARE,
        Permission.KB_CREATE, Permission.KB_READ, Permission.KB_UPDATE
    ],
    EnterpriseRole.DEPT_VIEWER: [
        Permission.DOC_READ, Permission.KB_READ
    ],
    EnterpriseRole.GUEST: [
        Permission.DOC_READ  # Only for specifically shared documents
    ]
}
```

### 3. API Architecture and Endpoints

#### New API Endpoints Structure

```python
# Company Management API
/api/v1/companies/
├── POST /create                    # Create new company
├── GET /list                      # List companies (system admin only)
├── GET /<company_id>              # Get company details
├── PUT /<company_id>              # Update company
├── DELETE /<company_id>           # Delete company
└── GET /<company_id>/analytics    # Company analytics

# Department Management API
/api/v1/companies/<company_id>/departments/
├── POST /create                   # Create department
├── GET /list                     # List departments
├── GET /<dept_id>                # Get department details
├── PUT /<dept_id>                # Update department
├── DELETE /<dept_id>             # Delete department
├── POST /<dept_id>/users         # Add user to department
├── DELETE /<dept_id>/users/<user_id>  # Remove user from department
└── GET /<dept_id>/analytics      # Department analytics

# Enhanced User Management API
/api/v1/users/
├── POST /invite                  # Invite user to company/department
├── GET /profile                  # Get current user profile with all roles
├── PUT /profile                  # Update user profile
├── GET /permissions              # Get user's effective permissions
└── POST /switch-context          # Switch active department context

# Document Sharing API
/api/v1/documents/<doc_id>/sharing/
├── POST /share                   # Share document with department
├── GET /shares                   # List document shares
├── PUT /shares/<share_id>        # Update share permissions
├── DELETE /shares/<share_id>     # Remove share
└── GET /audit                    # Get document audit log

# Enhanced Knowledge Base API (modified existing)
/api/v1/kb/
├── GET /accessible               # List accessible KBs across departments
├── POST /<kb_id>/share          # Share KB with department
└── GET /<kb_id>/permissions     # Get KB permissions for current user
```

#### Modified Existing Endpoints

```python
# Enhanced authentication to include department context
@manager.route('/login', methods=['POST'])
def login():
    # ... existing login logic ...
    # Add department context to response
    user_departments = UserCompanyDepartmentService.get_user_departments(user.id)
    return {
        'user': user.to_json(),
        'departments': user_departments,
        'default_department': user_departments[0] if user_departments else None
    }

# Enhanced KB list with department filtering
@manager.route('/list', methods=['POST'])
@login_required
@department_context_required
def list_kbs():
    current_dept = get_current_department_context()
    accessible_kbs = KnowledgebaseService.get_accessible_kbs(
        user_id=current_user.id,
        department_id=current_dept.id,
        include_shared=True
    )
    return get_json_result(data=accessible_kbs)
```

### 4. Authentication and Authorization System

#### Enhanced Authentication Flow

```python
# New authentication middleware
class EnterpriseAuthMiddleware:
    def __init__(self, app):
        self.app = app

    def __call__(self, environ, start_response):
        # Extract department context from headers or session
        dept_context = self.extract_department_context(environ)
        if dept_context:
            environ['DEPARTMENT_CONTEXT'] = dept_context
        return self.app(environ, start_response)

    def extract_department_context(self, environ):
        # Check for X-Department-ID header
        dept_id = environ.get('HTTP_X_DEPARTMENT_ID')
        if dept_id:
            return DepartmentService.get_by_id(dept_id)
        return None

# Enhanced authorization decorators
def require_permission(permission: Permission, resource_type: str = None):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not has_permission(current_user, permission, resource_type, kwargs):
                return get_json_result(
                    data=False,
                    message="Insufficient permissions",
                    code=settings.RetCode.FORBIDDEN
                )
            return func(*args, **kwargs)
        return wrapper
    return decorator

def department_context_required(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        dept_context = get_current_department_context()
        if not dept_context:
            return get_json_result(
                data=False,
                message="Department context required",
                code=settings.RetCode.BAD_REQUEST
            )
        kwargs['department_context'] = dept_context
        return func(*args, **kwargs)
    return wrapper

# Permission checking logic
def has_permission(user, permission: Permission, resource_type: str = None, context: dict = None):
    user_roles = UserCompanyDepartmentService.get_user_roles(user.id)

    for role_assignment in user_roles:
        role_permissions = ROLE_PERMISSIONS.get(role_assignment.role, [])
        if permission in role_permissions:
            # Check resource-specific permissions
            if resource_type and context:
                return check_resource_permission(user, permission, resource_type, context, role_assignment)
            return True

    return False

def check_resource_permission(user, permission, resource_type, context, role_assignment):
    if resource_type == 'document':
        doc_id = context.get('doc_id')
        if doc_id:
            return DocumentService.user_has_access(user.id, doc_id, permission, role_assignment.department_id)
    elif resource_type == 'knowledgebase':
        kb_id = context.get('kb_id')
        if kb_id:
            return KnowledgebaseService.user_has_access(user.id, kb_id, permission, role_assignment.department_id)

    return False
```

### 5. Document Access Control Implementation

#### Enhanced Document Service

```python
class EnterpriseDocumentService(DocumentService):

    @classmethod
    def user_has_access(cls, user_id: str, doc_id: str, permission: Permission, department_id: str = None):
        """Check if user has specific permission for document"""

        # Get document details
        doc = cls.get_by_id(doc_id)
        if not doc:
            return False

        # Check direct department access
        if doc.department_id == department_id:
            return True

        # Check shared access
        shared_access = DocumentShareService.get_shared_access(doc_id, department_id)
        if shared_access:
            return cls._check_share_permission(shared_access, permission)

        # Check company-level access for company admins
        user_roles = UserCompanyDepartmentService.get_user_roles(user_id)
        for role in user_roles:
            if role.role == EnterpriseRole.COMPANY_ADMIN and role.company_id == doc.company_id:
                return True

        return False

    @classmethod
    def _check_share_permission(cls, shared_access, permission: Permission):
        """Check if shared access allows specific permission"""
        permission_mapping = {
            'READ_ONLY': [Permission.DOC_READ],
            'READ_WRITE': [Permission.DOC_READ, Permission.DOC_UPDATE],
            'ADMIN': [Permission.DOC_READ, Permission.DOC_UPDATE, Permission.DOC_DELETE, Permission.DOC_SHARE]
        }

        allowed_permissions = permission_mapping.get(shared_access.permission_level, [])
        return permission in allowed_permissions

    @classmethod
    def create_with_department(cls, user_id: str, department_id: str, **kwargs):
        """Create document with department context"""
        department = DepartmentService.get_by_id(department_id)
        if not department:
            raise ValueError("Invalid department")

        kwargs.update({
            'company_id': department.company_id,
            'department_id': department_id,
            'created_by': user_id,
            'access_level': 'DEPARTMENT'
        })

        doc_id = cls.save(**kwargs)

        # Log creation
        DocumentAuditService.log_action(
            document_id=doc_id,
            user_id=user_id,
            department_id=department_id,
            action='CREATE',
            details={'initial_access_level': 'DEPARTMENT'}
        )

        return doc_id

class DocumentShareService(CommonService):
    model = DocumentShare

    @classmethod
    def share_document(cls, doc_id: str, shared_by_user_id: str, shared_by_dept_id: str,
                      shared_with_dept_id: str, permission_level: str, expires_at: datetime = None):
        """Share document between departments"""

        # Validate permissions
        if not DocumentService.user_has_access(shared_by_user_id, doc_id, Permission.DOC_SHARE, shared_by_dept_id):
            raise PermissionError("User does not have share permission")

        share_data = {
            'id': get_uuid(),
            'document_id': doc_id,
            'shared_by_user_id': shared_by_user_id,
            'shared_by_department_id': shared_by_dept_id,
            'shared_with_department_id': shared_with_dept_id,
            'permission_level': permission_level,
            'expires_at': expires_at,
            'status': '1'
        }

        share_id = cls.save(**share_data)

        # Log sharing action
        DocumentAuditService.log_action(
            document_id=doc_id,
            user_id=shared_by_user_id,
            department_id=shared_by_dept_id,
            action='SHARE',
            details={
                'shared_with_department': shared_with_dept_id,
                'permission_level': permission_level,
                'expires_at': expires_at.isoformat() if expires_at else None
            }
        )

        return share_id

    @classmethod
    def get_shared_access(cls, doc_id: str, department_id: str):
        """Get shared access for document and department"""
        shares = cls.query(
            document_id=doc_id,
            shared_with_department_id=department_id,
            status='1'
        )

        # Filter out expired shares
        active_shares = [
            share for share in shares
            if not share.expires_at or share.expires_at > datetime.now()
        ]

        return active_shares[0] if active_shares else None

class DocumentAuditService(CommonService):
    model = DocumentAuditLog

    @classmethod
    def log_action(cls, document_id: str, user_id: str, department_id: str,
                  action: str, details: dict = None, request=None):
        """Log document access/modification action"""

        audit_data = {
            'id': get_uuid(),
            'document_id': document_id,
            'user_id': user_id,
            'department_id': department_id,
            'action': action,
            'details': details or {},
            'timestamp': datetime.now()
        }

        if request:
            audit_data.update({
                'ip_address': request.remote_addr,
                'user_agent': request.headers.get('User-Agent', '')
            })

        return cls.save(**audit_data)

    @classmethod
    def get_document_audit_trail(cls, doc_id: str, limit: int = 100):
        """Get audit trail for document"""
        return cls.query(
            document_id=doc_id,
            order_by='timestamp',
            reverse=True,
            limit=limit
        )
```

### 6. Migration Strategy

#### Phase 1: Database Schema Migration

```python
# Migration script: migrate_to_enterprise.py
class EnterpriseDataMigration:

    @classmethod
    def migrate_existing_tenants_to_companies(cls):
        """Convert existing tenants to companies with single departments"""

        existing_tenants = TenantService.get_all()

        for tenant in existing_tenants:
            # Create company from tenant
            company_data = {
                'id': tenant.id,  # Keep same ID for compatibility
                'name': tenant.name.replace("'s Kingdom", " Company"),
                'subscription_tier': 'enterprise',
                'status': '1',
                'created_by': tenant.id
            }
            CompanyService.save(**company_data)

            # Create default department
            dept_data = {
                'id': get_uuid(),
                'company_id': tenant.id,
                'name': 'General',
                'description': 'Default department',
                'department_code': 'GEN',
                'status': '1',
                'created_by': tenant.id
            }
            dept_id = DepartmentService.save(**dept_data)

            # Migrate user-tenant relationships
            user_tenants = UserTenantService.query(tenant_id=tenant.id)
            for user_tenant in user_tenants:
                # Map old roles to new enterprise roles
                new_role = cls._map_legacy_role(user_tenant.role)

                user_dept_data = {
                    'id': get_uuid(),
                    'user_id': user_tenant.user_id,
                    'company_id': tenant.id,
                    'department_id': dept_id,
                    'role': new_role,
                    'invited_by': user_tenant.invited_by,
                    'status': user_tenant.status
                }
                UserCompanyDepartmentService.save(**user_dept_data)

            # Update knowledge bases
            kbs = KnowledgebaseService.query(tenant_id=tenant.id)
            for kb in kbs:
                KnowledgebaseService.update_by_id(kb.id, {
                    'company_id': tenant.id,
                    'department_id': dept_id,
                    'visibility': 'DEPARTMENT'
                })

            # Update documents
            docs = DocumentService.query_by_tenant(tenant.id)
            for doc in docs:
                DocumentService.update_by_id(doc.id, {
                    'company_id': tenant.id,
                    'department_id': dept_id,
                    'access_level': 'DEPARTMENT'
                })

    @classmethod
    def _map_legacy_role(cls, legacy_role):
        """Map legacy UserTenantRole to new EnterpriseRole"""
        mapping = {
            UserTenantRole.OWNER: EnterpriseRole.COMPANY_ADMIN,
            UserTenantRole.ADMIN: EnterpriseRole.DEPT_ADMIN,
            UserTenantRole.NORMAL: EnterpriseRole.DEPT_MEMBER,
            UserTenantRole.INVITE: EnterpriseRole.DEPT_MEMBER
        }
        return mapping.get(legacy_role, EnterpriseRole.DEPT_MEMBER)

# Migration execution
def run_migration():
    """Execute the migration in phases"""

    print("Starting enterprise migration...")

    # Phase 1: Create new tables
    print("Phase 1: Creating new database tables...")
    create_enterprise_tables()

    # Phase 2: Migrate existing data
    print("Phase 2: Migrating existing data...")
    EnterpriseDataMigration.migrate_existing_tenants_to_companies()

    # Phase 3: Update API endpoints (gradual rollout)
    print("Phase 3: API endpoints updated (requires application restart)")

    print("Migration completed successfully!")
```

#### Phase 2: API Backward Compatibility

```python
# Compatibility layer for existing API endpoints
class LegacyAPICompatibility:

    @staticmethod
    def wrap_legacy_endpoint(func):
        """Wrapper to maintain backward compatibility"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Inject department context for legacy endpoints
            if not get_current_department_context():
                default_dept = UserCompanyDepartmentService.get_user_default_department(current_user.id)
                if default_dept:
                    set_department_context(default_dept)

            return func(*args, **kwargs)
        return wrapper

# Apply compatibility wrapper to existing endpoints
original_list_kbs = manager.route('/list', methods=['POST'])(list_kbs)
list_kbs = LegacyAPICompatibility.wrap_legacy_endpoint(original_list_kbs)
```

### 7. Implementation Guide

#### Step 1: Database Setup

```bash
# 1. Backup existing database
mysqldump -u username -p ragflow_db > ragflow_backup_$(date +%Y%m%d).sql

# 2. Run migration scripts
python api/db/migrations/001_create_enterprise_tables.py
python api/db/migrations/002_migrate_existing_data.py
python api/db/migrations/003_add_indexes_and_constraints.py
```

#### Step 2: Service Layer Implementation

```python
# api/db/services/company_service.py
class CompanyService(CommonService):
    model = Company

    @classmethod
    def create_company(cls, name: str, domain: str = None, created_by: str = None):
        """Create new company with default settings"""
        company_data = {
            'id': get_uuid(),
            'name': name,
            'domain': domain,
            'subscription_tier': 'enterprise',
            'max_users': 1000,
            'max_departments': 50,
            'settings': {
                'allow_external_sharing': True,
                'require_approval_for_sharing': False,
                'default_document_retention_days': 365
            },
            'status': '1',
            'created_by': created_by
        }
        return cls.save(**company_data)

    @classmethod
    def get_by_domain(cls, domain: str):
        """Get company by email domain"""
        companies = cls.query(domain=domain, status='1')
        return companies[0] if companies else None

# api/db/services/department_service.py
class DepartmentService(CommonService):
    model = Department

    @classmethod
    def create_department(cls, company_id: str, name: str, parent_id: str = None, created_by: str = None):
        """Create new department"""

        # Validate company exists
        company = CompanyService.get_by_id(company_id)
        if not company:
            raise ValueError("Company not found")

        # Generate unique department code
        dept_code = cls._generate_department_code(company_id, name)

        dept_data = {
            'id': get_uuid(),
            'company_id': company_id,
            'parent_department_id': parent_id,
            'name': name,
            'department_code': dept_code,
            'status': '1',
            'created_by': created_by
        }
        return cls.save(**dept_data)

    @classmethod
    def _generate_department_code(cls, company_id: str, name: str):
        """Generate unique department code within company"""
        base_code = ''.join([c.upper() for c in name if c.isalpha()])[:3]
        if len(base_code) < 3:
            base_code = base_code.ljust(3, 'X')

        counter = 1
        while True:
            code = f"{base_code}{counter:02d}" if counter > 1 else base_code
            existing = cls.query(company_id=company_id, department_code=code)
            if not existing:
                return code
            counter += 1

    @classmethod
    def get_company_departments(cls, company_id: str, include_hierarchy: bool = False):
        """Get all departments for a company"""
        departments = cls.query(company_id=company_id, status='1')

        if include_hierarchy:
            return cls._build_department_hierarchy(departments)

        return departments

    @classmethod
    def _build_department_hierarchy(cls, departments):
        """Build hierarchical structure of departments"""
        dept_dict = {dept.id: dept for dept in departments}
        root_depts = []

        for dept in departments:
            if dept.parent_department_id:
                parent = dept_dict.get(dept.parent_department_id)
                if parent:
                    if not hasattr(parent, 'children'):
                        parent.children = []
                    parent.children.append(dept)
            else:
                root_depts.append(dept)

        return root_depts

# api/db/services/user_company_department_service.py
class UserCompanyDepartmentService(CommonService):
    model = UserCompanyDepartment

    @classmethod
    def add_user_to_department(cls, user_id: str, company_id: str, department_id: str,
                              role: EnterpriseRole, invited_by: str = None):
        """Add user to department with specific role"""

        # Validate user doesn't already exist in department
        existing = cls.query(user_id=user_id, department_id=department_id)
        if existing:
            raise ValueError("User already exists in department")

        user_dept_data = {
            'id': get_uuid(),
            'user_id': user_id,
            'company_id': company_id,
            'department_id': department_id,
            'role': role.value,
            'invited_by': invited_by,
            'status': '1'
        }
        return cls.save(**user_dept_data)

    @classmethod
    def get_user_departments(cls, user_id: str):
        """Get all departments user belongs to"""
        return cls.query(user_id=user_id, status='1')

    @classmethod
    def get_user_roles(cls, user_id: str):
        """Get all roles for user across companies/departments"""
        user_depts = cls.get_user_departments(user_id)
        return [
            {
                'company_id': ud.company_id,
                'department_id': ud.department_id,
                'role': ud.role,
                'permissions': ROLE_PERMISSIONS.get(EnterpriseRole(ud.role), [])
            }
            for ud in user_depts
        ]

    @classmethod
    def get_user_default_department(cls, user_id: str):
        """Get user's default/primary department"""
        user_depts = cls.get_user_departments(user_id)
        if not user_depts:
            return None

        # Prioritize company admin roles, then department admin, then first department
        admin_roles = [ud for ud in user_depts if ud.role in [EnterpriseRole.COMPANY_ADMIN, EnterpriseRole.DEPT_ADMIN]]
        if admin_roles:
            return admin_roles[0]

        return user_depts[0]
```

#### Step 3: API Endpoint Implementation Examples

```python
# api/apps/company_app.py
@manager.route('/create', methods=['POST'])
@login_required
@require_permission(Permission.COMPANY_MANAGE_DEPTS)
@validate_request("name")
def create_company():
    """Create new company"""
    req = request.json
    company_name = req["name"]
    domain = req.get("domain")

    try:
        company_id = CompanyService.create_company(
            name=company_name,
            domain=domain,
            created_by=current_user.id
        )

        # Create default department
        dept_id = DepartmentService.create_department(
            company_id=company_id,
            name="General",
            created_by=current_user.id
        )

        # Add creator as company admin
        UserCompanyDepartmentService.add_user_to_department(
            user_id=current_user.id,
            company_id=company_id,
            department_id=dept_id,
            role=EnterpriseRole.COMPANY_ADMIN
        )

        return get_json_result(data={"company_id": company_id, "department_id": dept_id})
    except Exception as e:
        return server_error_response(e)

# api/apps/department_app.py
@manager.route('/<company_id>/departments/create', methods=['POST'])
@login_required
@require_permission(Permission.COMPANY_MANAGE_DEPTS, 'company')
@validate_request("name")
def create_department(company_id):
    """Create new department in company"""
    req = request.json
    dept_name = req["name"]
    parent_id = req.get("parent_department_id")

    try:
        dept_id = DepartmentService.create_department(
            company_id=company_id,
            name=dept_name,
            parent_id=parent_id,
            created_by=current_user.id
        )
        return get_json_result(data={"department_id": dept_id})
    except Exception as e:
        return server_error_response(e)

@manager.route('/<company_id>/departments/<dept_id>/users', methods=['POST'])
@login_required
@require_permission(Permission.DEPT_MANAGE_USERS, 'department')
@validate_request("user_email", "role")
def add_user_to_department(company_id, dept_id):
    """Add user to department"""
    req = request.json
    user_email = req["user_email"]
    role = req["role"]

    try:
        # Find user by email
        users = UserService.query(email=user_email)
        if not users:
            return get_data_error_result(message="User not found")

        user = users[0]

        # Validate role
        if role not in [r.value for r in EnterpriseRole]:
            return get_data_error_result(message="Invalid role")

        UserCompanyDepartmentService.add_user_to_department(
            user_id=user.id,
            company_id=company_id,
            department_id=dept_id,
            role=EnterpriseRole(role),
            invited_by=current_user.id
        )

        return get_json_result(data=True)
    except ValueError as e:
        return get_data_error_result(message=str(e))
    except Exception as e:
        return server_error_response(e)

# api/apps/document_sharing_app.py
@manager.route('/<doc_id>/sharing/share', methods=['POST'])
@login_required
@require_permission(Permission.DOC_SHARE, 'document')
@validate_request("target_department_id", "permission_level")
def share_document(doc_id):
    """Share document with another department"""
    req = request.json
    target_dept_id = req["target_department_id"]
    permission_level = req["permission_level"]
    expires_at = req.get("expires_at")

    current_dept = get_current_department_context()
    if not current_dept:
        return get_data_error_result(message="Department context required")

    try:
        if expires_at:
            expires_at = datetime.fromisoformat(expires_at)

        share_id = DocumentShareService.share_document(
            doc_id=doc_id,
            shared_by_user_id=current_user.id,
            shared_by_dept_id=current_dept.id,
            shared_with_dept_id=target_dept_id,
            permission_level=permission_level,
            expires_at=expires_at
        )

        return get_json_result(data={"share_id": share_id})
    except PermissionError as e:
        return get_json_result(
            data=False,
            message=str(e),
            code=settings.RetCode.FORBIDDEN
        )
    except Exception as e:
        return server_error_response(e)

@manager.route('/<doc_id>/sharing/audit', methods=['GET'])
@login_required
@require_permission(Permission.DOC_READ, 'document')
def get_document_audit_trail(doc_id):
    """Get audit trail for document"""
    try:
        audit_trail = DocumentAuditService.get_document_audit_trail(doc_id)
        return get_json_result(data=[audit.to_dict() for audit in audit_trail])
    except Exception as e:
        return server_error_response(e)
```

### 8. Security Considerations

#### Data Isolation
- **Row-Level Security**: All queries filtered by company_id and department_id
- **API-Level Filtering**: Middleware ensures users only access authorized resources
- **Database Constraints**: Foreign key constraints prevent orphaned records

#### Access Control
- **Principle of Least Privilege**: Users granted minimum necessary permissions
- **Role-Based Access**: Hierarchical roles with inherited permissions
- **Resource-Level Permissions**: Fine-grained control over individual documents/KBs

#### Audit and Compliance
- **Complete Audit Trail**: All document access and modifications logged
- **Data Retention**: Configurable retention policies per company
- **Export Capabilities**: Audit logs exportable for compliance reporting

#### Security Implementation

```python
# Enhanced security middleware
class SecurityMiddleware:

    @staticmethod
    def validate_department_access(user_id: str, department_id: str):
        """Validate user has access to department"""
        user_depts = UserCompanyDepartmentService.get_user_departments(user_id)
        dept_ids = [ud.department_id for ud in user_depts]
        return department_id in dept_ids

    @staticmethod
    def validate_cross_department_operation(source_dept_id: str, target_dept_id: str, user_id: str):
        """Validate user can perform operations between departments"""
        user_roles = UserCompanyDepartmentService.get_user_roles(user_id)

        # Check if user is company admin in either department's company
        for role in user_roles:
            if (role['department_id'] in [source_dept_id, target_dept_id] and
                role['role'] == EnterpriseRole.COMPANY_ADMIN):
                return True

        return False

    @staticmethod
    def sanitize_query_params(params: dict, allowed_fields: list):
        """Sanitize query parameters to prevent injection"""
        sanitized = {}
        for key, value in params.items():
            if key in allowed_fields:
                # Basic sanitization - extend as needed
                if isinstance(value, str):
                    sanitized[key] = value.strip()[:255]  # Limit length
                else:
                    sanitized[key] = value
        return sanitized
```

### 9. Performance Optimization

#### Database Optimization
- **Proper Indexing**: Indexes on company_id, department_id, user_id for fast lookups
- **Query Optimization**: Efficient joins and subqueries for permission checks
- **Connection Pooling**: Database connection pooling for high concurrency

#### Caching Strategy
```python
# Redis-based caching for permissions and department context
class PermissionCache:

    @staticmethod
    def get_user_permissions(user_id: str):
        """Get cached user permissions"""
        cache_key = f"user_permissions:{user_id}"
        cached = redis_client.get(cache_key)

        if cached:
            return json.loads(cached)

        # Fetch from database
        permissions = UserCompanyDepartmentService.get_user_roles(user_id)

        # Cache for 15 minutes
        redis_client.setex(cache_key, 900, json.dumps(permissions))
        return permissions

    @staticmethod
    def invalidate_user_permissions(user_id: str):
        """Invalidate user permissions cache"""
        cache_key = f"user_permissions:{user_id}"
        redis_client.delete(cache_key)

    @staticmethod
    def get_department_context(user_id: str, dept_id: str):
        """Get cached department context"""
        cache_key = f"dept_context:{user_id}:{dept_id}"
        cached = redis_client.get(cache_key)

        if cached:
            return json.loads(cached)

        # Fetch from database
        context = DepartmentService.get_user_department_context(user_id, dept_id)

        # Cache for 30 minutes
        redis_client.setex(cache_key, 1800, json.dumps(context))
        return context
```

#### Scalability Considerations
- **Horizontal Scaling**: Database sharding by company_id for large enterprises
- **Microservices**: Separate services for user management, document management, and sharing
- **Event-Driven Architecture**: Async processing for audit logs and notifications

### 10. Deployment and Monitoring

#### Deployment Strategy
1. **Blue-Green Deployment**: Zero-downtime migration to new enterprise system
2. **Feature Flags**: Gradual rollout of enterprise features
3. **Database Migration**: Phased migration with rollback capabilities

#### Monitoring and Alerting
```python
# Monitoring metrics for enterprise features
class EnterpriseMetrics:

    @staticmethod
    def track_document_sharing():
        """Track document sharing activity"""
        metrics.increment('document.shared')

    @staticmethod
    def track_permission_check(permission: str, granted: bool):
        """Track permission check results"""
        metrics.increment(f'permission.check.{permission}.{"granted" if granted else "denied"}')

    @staticmethod
    def track_department_activity(dept_id: str, action: str):
        """Track department-level activity"""
        metrics.increment(f'department.{action}', tags={'department_id': dept_id})
```

## Conclusion

This enterprise multi-tenant system design provides:

1. **Scalable Architecture**: Supports large enterprises with hierarchical organization
2. **Granular Security**: Fine-grained permissions and access control
3. **Flexible Sharing**: Secure inter-department document collaboration
4. **Complete Auditability**: Full audit trail for compliance requirements
5. **Migration Path**: Clear strategy for upgrading existing installations
6. **Performance Optimization**: Caching and indexing for enterprise scale

The implementation can be rolled out incrementally, maintaining backward compatibility while adding enterprise features progressively.
