run-be-service:
	tmux new-session -d -s backend 'cd .. && \
	cd miniconda3 && \
	. bin/activate && \
	conda activate ragflow && \
	cd .. && \
	cd ekoflow && \
	. .venv/bin/activate && \
	bash docker/launch_backend_service.sh'

run-fe-service:
	tmux new-session -d -s frontend 'cd web && npm run dev'

# Helper targets to attach to sessions
attach-be:
	tmux attach-session -t backend

attach-fe:
	tmux attach-session -t frontend

# Helper targets to stop services
stop-be:
	tmux kill-session -t backend

stop-fe:
	tmux kill-session -t frontend

# List running sessions
list-sessions:
	tmux list-sessions