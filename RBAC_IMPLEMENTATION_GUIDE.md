# Enterprise Multi-Tenant RAGFlow RBAC System Implementation

## Overview

This document describes the implementation of a comprehensive Role-Based Access Control (RBAC) system for the RAGFlow platform, designed to support enterprise multi-tenant environments with department-based organization and scalability for 1000+ users.

## Architecture

### Core Components

1. **Database Models** (`api/db/db_models.py`)
   - `Department`: Organizational units within the enterprise
   - `RBACRole`: Hierarchical roles with permission sets
   - `UserRoleAssignment`: User-role mappings with scope and expiration
   - `ChatPermission`: Granular chat access control

2. **Service Layer** (`api/db/services/rbac_service.py`)
   - `RBACService`: Role management and creation
   - `UserRoleService`: User-role assignment operations
   - `DepartmentService`: Department management
   - `ChatPermissionService`: Chat access control

3. **Utility Layer** (`api/utils/rbac_utils.py`)
   - `RBACPermissionChecker`: Permission validation logic
   - Decorators for endpoint protection
   - Helper functions for current user context

4. **API Layer** (`api/apps/rbac_app.py`)
   - RESTful endpoints for RBAC management
   - Department, role, and permission management
   - User assignment and access control

## Role Hierarchy

### System Level (Level 1)
- **Super Admin**: Complete system control
  - All permissions across all scopes
  - Can create departments and manage system-wide settings
  - Can assign any role to any user

### Department Level (Level 2)
- **Department Admin**: Department-wide control
  - Manage all tenants within their department
  - Assign tenant-level roles within their department
  - User management within department scope

### Tenant Level (Level 3)
- **Tenant Manager**: Full tenant control
  - Manage tenant settings and users
  - Grant chat permissions
  - Assign user roles within tenant

- **Senior User**: Advanced user capabilities
  - Create, read, update operations
  - Analytics and API access
  - Can invite other users

- **Basic User**: Standard user access
  - Create, read, update operations
  - API access for basic operations

- **Viewer**: Read-only access
  - View-only permissions
  - No modification capabilities

## Permission System

### Permission Categories

1. **Resource Permissions**
   - `create`: Create new resources
   - `read`: View existing resources
   - `update`: Modify existing resources
   - `delete`: Remove resources
   - `manage`: Full resource control

2. **Feature Permissions**
   - `chat_access`: Access to chat functionality
   - `analytics_access`: View analytics and reports
   - `api_access`: Use API endpoints
   - `admin_panel`: Access administrative interface

3. **User Management Permissions**
   - `invite_users`: Invite new users
   - `assign_roles`: Assign roles to users
   - `remove_users`: Remove users from system

4. **System Permissions**
   - `create_departments`: Create new departments
   - `manage_tenants`: Manage tenant configurations
   - `grant_tenant_access`: Grant access to tenants

### Scope Types

- **System**: Global system-wide permissions
- **Department**: Department-specific permissions
- **Tenant**: Tenant-specific permissions

## Key Features

### 1. Hierarchical Access Control
- Permissions cascade down the hierarchy
- Department Admins can manage all tenants in their department
- Super Admins have access to everything

### 2. Granular Chat Permissions
- Separate chat permission system for fine-grained control
- Time-based expiration support
- Audit trail for all permission grants

### 3. Multi-Tenant Support
- Tenants can belong to departments
- Cross-department collaboration with proper permissions
- Isolated tenant data with controlled sharing

### 4. Scalability Features
- Efficient database indexing for large user bases
- Optimized queries for permission checking
- Caching-friendly permission structure

### 5. Security Features
- Role assignment requires appropriate permissions
- Audit trails for all role changes
- Time-based role expiration
- Secure permission checking decorators

## API Endpoints

### Department Management
- `POST /v1/rbac/departments` - Create department (Super Admin)
- `GET /v1/rbac/departments` - List departments
- `PUT /v1/rbac/departments/{id}` - Update department (Dept Admin)
- `DELETE /v1/rbac/departments/{id}` - Delete department (Super Admin)

### Role Management
- `GET /v1/rbac/roles` - List available roles
- `POST /v1/rbac/roles/custom` - Create custom role (Super Admin)

### User Role Assignments
- `POST /v1/rbac/assignments` - Assign role to user
- `DELETE /v1/rbac/assignments/{id}` - Revoke role assignment
- `GET /v1/rbac/users/{id}/roles` - Get user roles
- `GET /v1/rbac/users/current/roles` - Get current user roles

### Chat Permissions
- `POST /v1/rbac/chat-permissions` - Grant chat permission
- `DELETE /v1/rbac/chat-permissions/{user_id}/{tenant_id}` - Revoke chat permission
- `GET /v1/rbac/users/{id}/chat-permissions` - Get user chat permissions

### Permission Checking
- `POST /v1/rbac/check-permission` - Check specific permission
- `POST /v1/rbac/check-role` - Check specific role
- `GET /v1/rbac/users/current/accessible-tenants` - Get accessible tenants

## Usage Examples

### 1. Creating a Department
```python
from api.db.services.rbac_service import DepartmentService

department = DepartmentService.create_department(
    name="Engineering",
    description="Engineering Department",
    created_by=super_admin_id
)
```

### 2. Assigning a Role
```python
from api.db.services.rbac_service import UserRoleService, RBACService

# Get the role
role = RBACService.get_role_by_code(RBACRoleCode.DEPARTMENT_ADMIN)

# Assign to user
assignment = UserRoleService.assign_role(
    user_id=user_id,
    role_id=role.id,
    scope_type=ScopeType.DEPARTMENT,
    scope_id=department_id,
    granted_by=current_user_id
)
```

### 3. Checking Permissions
```python
from api.utils.rbac_utils import RBACPermissionChecker

# Check if user can create resources in a tenant
can_create = RBACPermissionChecker.user_has_permission(
    user_id, 'create', ScopeType.TENANT, tenant_id
)

# Check if user is a department admin
is_dept_admin = RBACPermissionChecker.user_is_department_admin(
    user_id, department_id
)
```

### 4. Using Decorators
```python
from api.utils.rbac_utils import require_permission, require_super_admin

@require_super_admin
def admin_only_endpoint():
    # Only Super Admins can access this
    pass

@require_permission('create', ScopeType.TENANT, 'tenant_id')
def create_resource(tenant_id):
    # User must have create permission in the specified tenant
    pass
```

## Installation and Setup

### 1. Service Startup and Database Migration
The RBAC system automatically initializes when the service starts:
```bash
# Start the RAGFlow backend service using the project's standard command
make run-be-service

# The service will start in a tmux session and automatically:
# - Create new RBAC tables
# - Create default roles
# - Initialize Super Admin account
```

### 2. Environment Variables
```bash
# Optional: Set Super Admin credentials
export RBAC_SUPER_ADMIN_EMAIL="<EMAIL>"
export RBAC_SUPER_ADMIN_PASSWORD="secure_password"
```

### 3. Testing
Verify the RBAC implementation:
```bash
# Ensure you're using the same environment as the service
source .venv/bin/activate

# Test RBAC component loading
python3 test_rbac_simple.py

# Monitor service logs for RBAC initialization
make attach-be
```

## Security Considerations

1. **Principle of Least Privilege**: Users are assigned minimal necessary permissions
2. **Role Separation**: Clear separation between system, department, and tenant roles
3. **Audit Trails**: All role assignments and permission grants are logged
4. **Time-based Expiration**: Roles and permissions can have expiration dates
5. **Secure Defaults**: New users get minimal permissions by default

## Performance Optimization

1. **Database Indexing**: Optimized indexes for permission queries
2. **Efficient Queries**: Minimized database calls for permission checking
3. **Caching Strategy**: Permission results can be cached for better performance
4. **Batch Operations**: Support for bulk role assignments

## Monitoring and Maintenance

1. **Role Usage Analytics**: Track which roles are most commonly used
2. **Permission Audit Reports**: Regular reports on permission assignments
3. **Expired Role Cleanup**: Automatic cleanup of expired role assignments
4. **Security Monitoring**: Monitor for unusual permission patterns

## Future Enhancements

1. **Dynamic Permissions**: Runtime permission modification
2. **Role Templates**: Pre-configured role sets for common scenarios
3. **Advanced Audit Logging**: Detailed audit trails with search capabilities
4. **Integration APIs**: External system integration for user management
5. **Mobile App Support**: Mobile-specific permission handling

This RBAC system provides a robust foundation for enterprise-scale multi-tenant applications with comprehensive security, scalability, and maintainability features.
