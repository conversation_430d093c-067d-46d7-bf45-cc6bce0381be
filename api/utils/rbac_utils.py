#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import logging
from functools import wraps
from typing import List, Dict, Set, Optional
from flask import request
from flask_login import current_user

from api.db import RB<PERSON>RoleCode, ScopeType, StatusEnum
from api.db.services.rbac_service import (
    RBACService, UserRoleService, ChatPermissionService, Permission
)
from api.utils.api_utils import get_json_result
from api import settings


class RBACPermissionChecker:
    """Utility class for checking RBAC permissions"""
    
    @staticmethod
    def user_has_permission(user_id: str, permission_type: str, scope_type: str = ScopeType.SYSTEM, scope_id: Optional[str] = None) -> bool:
        """Check if user has a specific permission"""
        user_roles = UserRoleService.get_user_roles(user_id)
        
        for role in user_roles:
            # Check if role matches the scope
            if role['scope_type'] != scope_type:
                continue
            
            if scope_id and role['scope_id'] != scope_id:
                continue
            
            # Check if role has the permission
            permissions = role.get('permissions', {})
            if RBACPermissionChecker._role_has_permission(permissions, permission_type):
                return True
        
        return False
    
    @staticmethod
    def _role_has_permission(permissions: Dict, permission_type: str) -> bool:
        """Check if a role's permission set includes the permission type"""
        for category, perms in permissions.items():
            if permission_type in perms:
                return True
        return False
    
    @staticmethod
    def user_has_role(user_id: str, role_code: str, scope_type: Optional[str] = None, scope_id: Optional[str] = None) -> bool:
        """Check if user has a specific role"""
        print(f"Checking role: {role_code}, scope_type: {scope_type}, scope_id: {scope_id}")
        user_roles = UserRoleService.get_user_roles(user_id)
        
        for role in user_roles:
            if role['role_code'] != role_code:
                continue
            
            if scope_type and role['scope_type'] != scope_type:
                continue
            
            if scope_id and role['scope_id'] != scope_id:
                continue
            
            return True
        
        return False
    
    @staticmethod
    def user_is_super_admin(user_id: str) -> bool:
        """Check if user is Super Admin"""
        return RBACPermissionChecker.user_has_role(user_id, RBACRoleCode.SUPER_ADMIN, ScopeType.SYSTEM)
    
    @staticmethod
    def user_is_department_admin(user_id: str, department_id: str = None) -> bool:
        """Check if user is Department Admin"""
        return RBACPermissionChecker.user_has_role(
            user_id, RBACRoleCode.DEPARTMENT_ADMIN, ScopeType.DEPARTMENT, department_id
        )
    
    @staticmethod
    def user_is_tenant_manager(user_id: str, tenant_id: str = None) -> bool:
        """Check if user is Tenant Manager"""
        return RBACPermissionChecker.user_has_role(
            user_id, RBACRoleCode.TENANT_MANAGER, ScopeType.TENANT, tenant_id
        )
    
    @staticmethod
    def user_can_grant_chat_access(user_id: str, tenant_id: str) -> bool:
        """Check if user can grant chat access for a tenant"""
        return RBACPermissionChecker.user_is_tenant_manager(user_id, tenant_id)
    
    @staticmethod
    def get_user_accessible_tenants(user_id: str) -> List[str]:
        """Get list of tenant IDs user has access to"""
        user_roles = UserRoleService.get_user_roles(user_id)
        tenant_ids = set()
        
        for role in user_roles:
            if role['scope_type'] == ScopeType.TENANT and role['scope_id']:
                tenant_ids.add(role['scope_id'])
            elif role['scope_type'] == ScopeType.DEPARTMENT and role['scope_id']:
                # Department admins have access to all tenants in their department
                from api.db.services.rbac_service import DepartmentService
                dept_tenants = DepartmentService.get_department_tenants(role['scope_id'])
                tenant_ids.update([t.id for t in dept_tenants])
        
        return list(tenant_ids)


# Permission Decorators
def require_permission(permission_type: str, scope_type: str = ScopeType.SYSTEM, scope_id_param: Optional[str] = None):
    """Decorator to require specific permission for an endpoint"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return get_json_result(
                    data=False,
                    message='Authentication required.',
                    code=settings.RetCode.AUTHENTICATION_ERROR
                )
            
            # Get scope_id from request parameters if specified
            scope_id = None
            if scope_id_param:
                scope_id = kwargs.get(scope_id_param) or request.json.get(scope_id_param) if request.json else None

                # If scope_id is still None, try to get it from user context
                if scope_id is None:
                    if scope_id_param == 'tenant_id' and scope_type == ScopeType.TENANT:
                        scope_id = current_user.id
                    elif scope_id_param == 'department_id' and scope_type == ScopeType.DEPARTMENT:
                        # Get user's department from their role assignments
                        user_roles = UserRoleService.get_user_roles(current_user.id)
                        for role in user_roles:
                            if role['scope_type'] == ScopeType.DEPARTMENT and role['scope_id']:
                                scope_id = role['scope_id']
                                break

                        # If user has no department assignment, deny access
                        if scope_id is None:
                            return get_json_result(
                                data=False,
                                message='Access denied. User must be assigned to a department to create knowledge bases.',
                                code=settings.RetCode.AUTHENTICATION_ERROR
                            )
            
            # Check permission
            if not RBACPermissionChecker.user_has_permission(
                current_user.id, permission_type, scope_type, scope_id
            ):
                return get_json_result(
                    data=False,
                    message=f'Permission denied. Required: {permission_type} in {scope_type}',
                    code=settings.RetCode.AUTHENTICATION_ERROR
                )
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def require_role(role_code: str, scope_type: str = ScopeType.SYSTEM, scope_id_param: Optional[str] = None):
    """Decorator to require specific role for an endpoint"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return get_json_result(
                    data=False,
                    message='Authentication required.',
                    code=settings.RetCode.AUTHENTICATION_ERROR
                )
            
            # Get scope_id from request parameters if specified
            scope_id = None
            if scope_id_param:
                scope_id = kwargs.get(scope_id_param) or request.json.get(scope_id_param) if request.json else None

                # If scope_id is still None, try to get it from user context
                if scope_id is None:
                    if scope_id_param == 'tenant_id' and scope_type == ScopeType.TENANT:
                        scope_id = current_user.id
                    elif scope_id_param == 'department_id' and scope_type == ScopeType.DEPARTMENT:
                        # Get user's department from their role assignments
                        user_roles = UserRoleService.get_user_roles(current_user.id)
                        for role in user_roles:
                            if role['scope_type'] == ScopeType.DEPARTMENT and role['scope_id']:
                                scope_id = role['scope_id']
                                break

                        # If user has no department assignment, deny access
                        if scope_id is None:
                            return get_json_result(
                                data=False,
                                message='Access denied. User must be assigned to a department.',
                                code=settings.RetCode.AUTHENTICATION_ERROR
                            )
            
            # Check role
            if not RBACPermissionChecker.user_has_role(
                current_user.id, role_code, scope_type, scope_id
            ):
                return get_json_result(
                    data=False,
                    message=f'Role required: {role_code} in {scope_type}',
                    code=settings.RetCode.AUTHENTICATION_ERROR
                )
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def require_chat_permission(tenant_id_param: str = 'tenant_id'):
    """Decorator to require chat permission for a tenant"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return get_json_result(
                    data=False,
                    message='Authentication required.',
                    code=settings.RetCode.AUTHENTICATION_ERROR
                )
            
            # Get tenant_id from request parameters
            tenant_id = kwargs.get(tenant_id_param) or request.json.get(tenant_id_param) if request.json else None
            
            if not tenant_id:
                return get_json_result(
                    data=False,
                    message='Tenant ID required for chat access.',
                    code=settings.RetCode.ARGUMENT_ERROR
                )
            
            # Check chat permission
            if not ChatPermissionService.user_has_chat_access(current_user.id, tenant_id):
                return get_json_result(
                    data=False,
                    message='Chat access denied for this tenant.',
                    code=settings.RetCode.AUTHENTICATION_ERROR
                )
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def require_super_admin(f):
    """Decorator to require Super Admin role"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return get_json_result(
                data=False,
                message='Authentication required.',
                code=settings.RetCode.AUTHENTICATION_ERROR
            )
        
        if not RBACPermissionChecker.user_is_super_admin(current_user.id):
            return get_json_result(
                data=False,
                message='Super Admin access required.',
                code=settings.RetCode.AUTHENTICATION_ERROR
            )
        
        return f(*args, **kwargs)
    return decorated_function


# Utility Functions
def current_user_has_permission(permission_type: str, scope_type: str = ScopeType.SYSTEM, scope_id: Optional[str] = None) -> bool:
    """Check if current authenticated user has permission"""
    if not current_user.is_authenticated:
        return False
    
    return RBACPermissionChecker.user_has_permission(
        current_user.id, permission_type, scope_type, scope_id
    )


def current_user_has_role(role_code: str, scope_type: Optional[str] = None, scope_id: Optional[str] = None) -> bool:
    """Check if current authenticated user has role"""
    if not current_user.is_authenticated:
        return False
    
    return RBACPermissionChecker.user_has_role(
        current_user.id, role_code, scope_type, scope_id
    )


def current_user_has_chat_access(tenant_id: str) -> bool:
    """Check if current authenticated user has chat access for tenant"""
    if not current_user.is_authenticated:
        return False
    
    return ChatPermissionService.user_has_chat_access(current_user.id, tenant_id)


def get_current_user_roles() -> List[Dict]:
    """Get all roles for current authenticated user"""
    if not current_user.is_authenticated:
        return []
    
    return UserRoleService.get_user_roles(current_user.id)


def get_current_user_accessible_tenants() -> List[str]:
    """Get tenants accessible by current user"""
    if not current_user.is_authenticated:
        return []
    
    return RBACPermissionChecker.get_user_accessible_tenants(current_user.id)
