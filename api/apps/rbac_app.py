#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

from flask import request
from flask_login import login_required, current_user

from api import settings
from api.db import RB<PERSON>RoleCode, ScopeType, StatusEnum
from api.db.db_models import User
from api.db.services.rbac_service import RBACService, UserRoleService, DepartmentService
from api.db.services.user_service import UserService
from api.utils.api_utils import get_json_result, validate_request, server_error_response


# Temporary Super Admin Setup Endpoint (NO AUTH REQUIRED)
@manager.route('/setup-super-admin', methods=['POST'])  # noqa: F821
@validate_request("user_email")
def setup_super_admin():
    """
    Temporary endpoint for initial Super Admin setup
    NO AUTHENTICATION REQUIRED - Use only for bootstrapping the first Super Admin
    This endpoint should be removed after initial setup
    """
    try:
        req = request.json
        user_email = req['user_email']

        # Check if any Super Admins already exist
        existing_super_admins = UserRoleService.get_users_with_role(
            RBACRoleCode.SUPER_ADMIN, ScopeType.SYSTEM
        )

        print("existing super admin", existing_super_admins)

        if existing_super_admins:
            return get_json_result(
                data=False,
                code=settings.RetCode.DATA_ERROR,
                message="Super Admin already exists. Use the authenticated promotion endpoint instead."
            )

        # Find the user
        users = UserService.query(email=user_email)
        if not users:
            return get_json_result(
                data=False,
                code=settings.RetCode.DATA_ERROR,
                message=f"User with email {user_email} not found. Please register first."
            )

        user = users[0]

        # Get Super Admin role
        super_admin_role = RBACService.get_role_by_code(RBACRoleCode.SUPER_ADMIN)
        if not super_admin_role:
            return get_json_result(
                data=False,
                code=settings.RetCode.DATA_ERROR,
                message="Super Admin role not found. Initialize RBAC system first."
            )

        # Assign Super Admin role
        assignment = UserRoleService.assign_role(
            user_id=user.id,
            role_id=super_admin_role.id,
            scope_type=ScopeType.SYSTEM,
            granted_by=user.id  # Self-granted for initial setup
        )

        # Update user flags for compatibility
        User.update(
            is_super_admin=True,
            is_superuser=True
        ).where(User.id == user.id).execute()

        # Refresh user object to get updated values
        user = User.get_by_id(user.id)

        return get_json_result(data={
            'user_id': user.id,
            'email': user_email,
            'nickname': user.nickname,
            'role_assignment_id': assignment.id,
            'message': f'User {user_email} has been set up as the first Super Admin',
            'warning': 'This setup endpoint should be removed after initial configuration'
        })

    except Exception as e:
        return server_error_response(e)


# Super Admin Promotion Endpoint (AUTH REQUIRED)
@manager.route('/promote-to-super-admin', methods=['POST'])  # noqa: F821
@login_required
@validate_request("user_email")
def promote_to_super_admin():
    """
    Temporary endpoint to promote a user to Super Admin
    This should only be used during initial setup
    """
    try:
        req = request.json
        user_email = req['user_email']

        # Find the user
        users = UserService.query(email=user_email)
        if not users:
            return get_json_result(
                data=False,
                code=settings.RetCode.DATA_ERROR,
                message=f"User with email {user_email} not found"
            )

        user = users[0]

        # Check if any Super Admins exist
        existing_super_admins = UserRoleService.get_users_with_role(
            RBACRoleCode.SUPER_ADMIN, ScopeType.SYSTEM
        )

        # If no Super Admins exist, allow self-promotion for initial setup
        if not existing_super_admins:
            if user.id != current_user.id:
                return get_json_result(
                    data=False,
                    code=settings.RetCode.AUTHENTICATION_ERROR,
                    message="Only the requesting user can become the first Super Admin"
                )
        else:
            # If Super Admins exist, check if current user is one
            is_current_user_super_admin = any(
                admin['user_id'] == current_user.id for admin in existing_super_admins
            )
            if not is_current_user_super_admin:
                return get_json_result(
                    data=False,
                    code=settings.RetCode.AUTHENTICATION_ERROR,
                    message="Only existing Super Admins can promote other users"
                )

            # Check if target user is already a Super Admin
            if any(admin['user_id'] == user.id for admin in existing_super_admins):
                return get_json_result(
                    data=False,
                    code=settings.RetCode.DATA_ERROR,
                    message=f"User {user_email} is already a Super Admin"
                )

        # Get Super Admin role
        super_admin_role = RBACService.get_role_by_code(RBACRoleCode.SUPER_ADMIN)
        if not super_admin_role:
            return get_json_result(
                data=False,
                code=settings.RetCode.DATA_ERROR,
                message="Super Admin role not found. Initialize RBAC system first."
            )

        # Assign Super Admin role
        assignment = UserRoleService.assign_role(
            user_id=user.id,
            role_id=super_admin_role.id,
            scope_type=ScopeType.SYSTEM,
            granted_by=current_user.id
        )

        # Update user flags for compatibility
        User.update(
            is_super_admin=True,
            is_superuser=True
        ).where(User.id == user.id).execute()

        # Refresh user object to get updated values
        user = User.get_by_id(user.id)

        return get_json_result(data={
            'user_id': user.id,
            'email': user_email,
            'nickname': user.nickname,
            'role_assignment_id': assignment.id,
            'message': f'User {user_email} has been promoted to Super Admin'
        })

    except Exception as e:
        return server_error_response(e)


# Get current user's roles
@manager.route('/users/current/roles', methods=['GET'])  # noqa: F821
@login_required
def get_current_user_roles():
    """Get current user's RBAC roles"""
    try:
        user_roles = UserRoleService.get_user_roles(current_user.id)
        return get_json_result(data=user_roles)
    except Exception as e:
        return server_error_response(e)


# Get current user's permissions
@manager.route('/users/current/permissions', methods=['GET'])  # noqa: F821
@login_required
def get_current_user_permissions():
    """Get current user's comprehensive permissions"""
    try:
        # Get user's role assignments
        user_roles = UserRoleService.get_user_roles(current_user.id)

        # Aggregate all permissions
        all_permissions = set()
        role_details = []

        for role in user_roles:
            role_details.append({
                'role_code': role['role_code'],
                'role_name': role['role_name'],
                'scope_type': role['scope_type'],
                'scope_id': role['scope_id']
            })

            # Collect all permissions from this role
            permissions = role.get('permissions', {})
            for perms in permissions.values():
                all_permissions.update(perms)

        return get_json_result(data={
            'user_id': current_user.id,
            'roles': role_details,
            'permissions': list(all_permissions),
            'permission_count': len(all_permissions)
        })
    except Exception as e:
        return server_error_response(e)


# List all roles (for testing)
@manager.route('/roles', methods=['GET'])  # noqa: F821
@login_required
def list_roles():
    """List all RBAC roles"""
    try:
        # Get all roles by querying the model directly
        roles = list(RBACService.model.select())
        roles_data = []
        for role in roles:
            roles_data.append({
                'id': role.id,
                'name': role.name,
                'code': role.code,
                'description': role.description,
                'level': role.level,
                'permissions': role.permissions,
                'is_system_role': role.is_system_role,
                'status': role.status
            })
        return get_json_result(data=roles_data)
    except Exception as e:
        return server_error_response(e)


# Health check endpoint
@manager.route('/health', methods=['GET'])  # noqa: F821
def rbac_health():
    """RBAC system health check"""
    try:
        # Check if roles exist
        roles = list(RBACService.model.select())
        role_count = len(roles)

        # Check if any Super Admins exist
        super_admins = UserRoleService.get_users_with_role(
            RBACRoleCode.SUPER_ADMIN, ScopeType.SYSTEM
        )
        super_admin_count = len(super_admins)

        return get_json_result(data={
            'rbac_initialized': role_count > 0,
            'total_roles': role_count,
            'super_admin_count': super_admin_count,
            'status': 'healthy'
        })
    except Exception as e:
        return server_error_response(e)


# Department Management Endpoints
@manager.route('/departments', methods=['POST'])  # noqa: F821
@login_required
@validate_request("name")
def create_department():
    """Create a new department (Super Admin only)"""
    try:
        req = request.json
        name = req['name']
        description = req.get('description')

        # Check if user has permission to create departments
        # Only Super Admins can create departments
        user_roles = UserRoleService.get_user_roles(current_user.id)
        is_super_admin = any(
            role['role_code'] == RBACRoleCode.SUPER_ADMIN and role['scope_type'] == ScopeType.SYSTEM
            for role in user_roles
        )

        if not is_super_admin:
            return get_json_result(
                data=False,
                code=settings.RetCode.AUTHENTICATION_ERROR,
                message="Only Super Admins can create departments"
            )

        # Create department
        department = DepartmentService.create_department(
            name=name,
            description=description,
            created_by=current_user.id
        )

        return get_json_result(data={
            'id': department.id,
            'name': department.name,
            'description': department.description,
            'created_by': department.created_by,
            'created_at': department.create_time,
            'message': f'Department "{name}" created successfully'
        })

    except Exception as e:
        return server_error_response(e)


@manager.route('/departments', methods=['GET'])  # noqa: F821
@login_required
def list_departments():
    """List all departments"""
    try:
        departments = list(DepartmentService.model.select().where(
            DepartmentService.model.status == StatusEnum.VALID.value
        ))

        departments_data = []
        for dept in departments:
            departments_data.append({
                'id': dept.id,
                'name': dept.name,
                'description': dept.description,
                'created_by': dept.created_by,
                'created_at': dept.create_time,
                'status': dept.status
            })

        return get_json_result(data=departments_data)

    except Exception as e:
        return server_error_response(e)


@manager.route('/assignments', methods=['POST'])  # noqa: F821
@login_required
@validate_request("user_id", "role_code", "scope_type")
def assign_role_to_user():
    """Assign role to user"""
    try:
        req = request.json
        user_id = req['user_id']
        role_code = req['role_code']
        scope_type = req['scope_type']
        scope_id = req.get('scope_id')

        # Permission check for Super Admin role assignment
        if role_code == RBACRoleCode.SUPER_ADMIN:
            user_roles = UserRoleService.get_user_roles(current_user.id)
            is_super_admin = any(
                role['role_code'] == RBACRoleCode.SUPER_ADMIN and role['scope_type'] == ScopeType.SYSTEM
                for role in user_roles
            )
            if not is_super_admin:
                return get_json_result(
                    data=False,
                    message='Only Super Admin can assign Super Admin role.',
                    code=settings.RetCode.AUTHENTICATION_ERROR
                )

        # Get role
        role = RBACService.get_role_by_code(role_code)
        if not role:
            return get_json_result(
                data=False,
                code=settings.RetCode.DATA_ERROR,
                message=f"Role {role_code} not found"
            )

        # Assign role
        assignment = UserRoleService.assign_role(
            user_id=user_id,
            role_id=role.id,
            scope_type=scope_type,
            scope_id=scope_id,
            granted_by=current_user.id
        )

        return get_json_result(data={
            'assignment_id': assignment.id,
            'user_id': user_id,
            'role_code': role_code,
            'scope_type': scope_type,
            'scope_id': scope_id,
            'message': f'Role {role_code} assigned successfully'
        })

    except Exception as e:
        return server_error_response(e)