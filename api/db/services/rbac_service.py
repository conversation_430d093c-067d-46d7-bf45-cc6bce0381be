#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import logging
from datetime import datetime
from typing import List, Dict, Optional, Set

import peewee
from api.db import (
    RBACRoleCode, RBACRoleLevel, ScopeType, PermissionType, 
    DEFAULT_ROLE_PERMISSIONS, StatusEnum
)
from api.db.db_models import (
    DB, Department, RBACRole, UserRoleAssignment, ChatPermission, 
    User, Tenant, UserTenant
)
from api.db.services.common_service import CommonService
from api.utils import get_uuid, current_timestamp


class Permission:
    """Represents a permission with scope and type"""
    
    def __init__(self, permission_type: str, scope_type: str = ScopeType.SYSTEM, scope_id: str = None):
        self.permission_type = permission_type
        self.scope_type = scope_type
        self.scope_id = scope_id
    
    def __str__(self):
        if self.scope_id:
            return f"{self.permission_type}@{self.scope_type}:{self.scope_id}"
        return f"{self.permission_type}@{self.scope_type}"
    
    def __eq__(self, other):
        if not isinstance(other, Permission):
            return False
        return (self.permission_type == other.permission_type and
                self.scope_type == other.scope_type and
                self.scope_id == other.scope_id)
    
    def __hash__(self):
        return hash((self.permission_type, self.scope_type, self.scope_id))


class RBACService(CommonService):
    """Service for managing RBAC roles and permissions"""
    
    model = RBACRole
    
    @classmethod
    @DB.connection_context()
    def create_default_roles(cls):
        """Create default system roles if they don't exist"""
        for role_code, permissions in DEFAULT_ROLE_PERMISSIONS.items():
            if not cls.get_role_by_code(role_code):
                cls.create_role(
                    name=role_code.replace('_', ' ').title(),
                    code=role_code,
                    description=f"Default {role_code.replace('_', ' ').title()} role",
                    level=cls._get_role_level(role_code),
                    permissions=permissions,
                    is_system_role=True
                )
    
    @classmethod
    @DB.connection_context()
    def create_role(cls, name: str, code: str, description: str, level: int,
                   permissions: Dict, is_system_role: bool = False) -> RBACRole:
        """Create a new role"""
        role_data = {
            'id': get_uuid(),
            'name': name,
            'code': code,
            'description': description,
            'level': level,
            'permissions': permissions,
            'is_system_role': is_system_role,
            'status': StatusEnum.VALID.value
        }

        role = cls.model(**role_data)
        role.save(force_insert=True)
        return role
    
    @classmethod
    @DB.connection_context()
    def get_role_by_code(cls, code: str) -> Optional[RBACRole]:
        """Get role by code"""
        try:
            return cls.model.select().where(
                (cls.model.code == code) & 
                (cls.model.status == StatusEnum.VALID.value)
            ).get()
        except peewee.DoesNotExist:
            return None
    
    @classmethod
    @DB.connection_context()
    def get_roles_by_level(cls, level: int) -> List[RBACRole]:
        """Get all roles at a specific level"""
        return list(cls.model.select().where(
            (cls.model.level == level) & 
            (cls.model.status == StatusEnum.VALID.value)
        ))
    
    @classmethod
    def _get_role_level(cls, role_code: str) -> int:
        """Get the level for a role code"""
        if role_code == RBACRoleCode.SUPER_ADMIN:
            return RBACRoleLevel.SYSTEM
        elif role_code == RBACRoleCode.DEPARTMENT_ADMIN:
            return RBACRoleLevel.DEPARTMENT
        else:
            return RBACRoleLevel.TENANT


class UserRoleService(CommonService):
    """Service for managing user role assignments"""
    
    model = UserRoleAssignment
    
    @classmethod
    @DB.connection_context()
    def assign_role(cls, user_id: str, role_id: str, scope_type: str,
                   scope_id: Optional[str] = None, granted_by: Optional[str] = None, expires_at: Optional[int] = None) -> UserRoleAssignment:
        """Assign a role to a user with specific scope"""
        
        # Check if assignment already exists
        existing = cls.get_user_role_assignment(user_id, role_id, scope_type, scope_id)
        if existing:
            raise ValueError("Role assignment already exists")
        
        assignment_data = {
            'id': get_uuid(),
            'user_id': user_id,
            'role_id': role_id,
            'scope_type': scope_type,
            'scope_id': scope_id,
            'granted_by': granted_by,
            'granted_at': current_timestamp(),
            'expires_at': expires_at,
            'status': StatusEnum.VALID.value
        }

        assignment = cls.model(**assignment_data)
        assignment.save(force_insert=True)
        return assignment
    
    @classmethod
    @DB.connection_context()
    def get_user_role_assignment(cls, user_id: str, role_id: str, scope_type: str, scope_id: Optional[str] = None) -> Optional[UserRoleAssignment]:
        """Get a specific user role assignment"""
        try:
            query = cls.model.select().where(
                (cls.model.user_id == user_id) &
                (cls.model.role_id == role_id) &
                (cls.model.scope_type == scope_type) &
                (cls.model.status == StatusEnum.VALID.value)
            )
            
            if scope_id:
                query = query.where(cls.model.scope_id == scope_id)
            else:
                query = query.where(cls.model.scope_id.is_null())
            
            return query.get()
        except peewee.DoesNotExist:
            return None
    
    @classmethod
    @DB.connection_context()
    def get_user_roles(cls, user_id: str) -> List[Dict]:
        """Get all active roles for a user with role details"""
        current_time = current_timestamp()
        
        query = (cls.model
                .select(cls.model, RBACRole)
                .join(RBACRole, on=(cls.model.role_id == RBACRole.id))
                .where(
                    (cls.model.user_id == user_id) &
                    (cls.model.status == StatusEnum.VALID.value) &
                    (RBACRole.status == StatusEnum.VALID.value) &
                    ((cls.model.expires_at.is_null()) | (cls.model.expires_at > current_time))
                ))
        
        roles = []
        for assignment in query:
            # Get the role from the join
            role = assignment.rbacrole
            roles.append({
                'assignment_id': assignment.id,
                'role_id': assignment.role_id,
                'role_code': role.code,
                'role_name': role.name,
                'scope_type': assignment.scope_type,
                'scope_id': assignment.scope_id,
                'permissions': role.permissions,
                'granted_at': assignment.granted_at,
                'expires_at': assignment.expires_at
            })

        print("roles", roles)
        
        return roles
    
    @classmethod
    @DB.connection_context()
    def revoke_role(cls, assignment_id: str) -> bool:
        """Revoke a role assignment"""
        try:
            cls.model.update({'status': StatusEnum.INVALID.value}).where(
                cls.model.id == assignment_id
            ).execute()
            return True
        except Exception as e:
            logging.error(f"Failed to revoke role assignment {assignment_id}: {e}")
            return False
    
    @classmethod
    @DB.connection_context()
    def get_users_with_role(cls, role_code: str, scope_type: str = None, scope_id: str = None) -> List[Dict]:
        """Get all users with a specific role"""
        query = (User
                .select(User, cls.model, RBACRole)
                .join(cls.model, on=(User.id == cls.model.user_id))
                .join(RBACRole, on=(cls.model.role_id == RBACRole.id))
                .where(
                    (RBACRole.code == role_code) &
                    (cls.model.status == StatusEnum.VALID.value) &
                    (RBACRole.status == StatusEnum.VALID.value) &
                    (User.status == StatusEnum.VALID.value)
                ))
        
        if scope_type:
            query = query.where(cls.model.scope_type == scope_type)
        
        if scope_id:
            query = query.where(cls.model.scope_id == scope_id)
        
        users = []
        for user in query:
            # Get the assignment from the join
            assignment = user.userroleassignment
            users.append({
                'user_id': user.id,
                'email': user.email,
                'nickname': user.nickname,
                'assignment_id': assignment.id,
                'scope_type': assignment.scope_type,
                'scope_id': assignment.scope_id,
                'granted_at': assignment.granted_at
            })
        
        return users


class DepartmentService(CommonService):
    """Service for managing departments"""

    model = Department

    @classmethod
    @DB.connection_context()
    def create_department(cls, name: str, description: Optional[str] = None, created_by: Optional[str] = None) -> Department:
        """Create a new department"""
        department_data = {
            'id': get_uuid(),
            'name': name,
            'description': description,
            'created_by': created_by,
            'status': StatusEnum.VALID.value
        }

        department = cls.model(**department_data)
        department.save(force_insert=True)
        return department

    @classmethod
    @DB.connection_context()
    def get_department_tenants(cls, department_id: str) -> List[Tenant]:
        """Get all tenants in a department"""
        return list(Tenant.select().where(
            (Tenant.department_id == department_id) &
            (Tenant.status == StatusEnum.VALID.value)
        ))

    @classmethod
    @DB.connection_context()
    def get_user_departments(cls, user_id: str) -> List[Dict]:
        """Get departments where user has department-level roles"""
        assignments = UserRoleService.get_user_roles(user_id)
        department_roles = [a for a in assignments if a['scope_type'] == ScopeType.DEPARTMENT]

        departments = []
        for role in department_roles:
            try:
                dept = cls.model.get_by_id(role['scope_id'])
                departments.append({
                    'department_id': dept.id,
                    'department_name': dept.name,
                    'role_code': role['role_code'],
                    'role_name': role['role_name']
                })
            except peewee.DoesNotExist:
                continue

        return departments


class ChatPermissionService(CommonService):
    """Service for managing chat permissions"""

    model = ChatPermission

    @classmethod
    @DB.connection_context()
    def grant_chat_permission(cls, user_id: str, tenant_id: str, granted_by: str, expires_at: Optional[int] = None) -> ChatPermission:
        """Grant chat permission to a user for a tenant"""

        # Check if permission already exists
        existing = cls.get_chat_permission(user_id, tenant_id)
        if existing:
            raise ValueError("Chat permission already exists")

        permission_data = {
            'id': get_uuid(),
            'user_id': user_id,
            'tenant_id': tenant_id,
            'granted_by': granted_by,
            'granted_at': current_timestamp(),
            'expires_at': expires_at,
            'status': StatusEnum.VALID.value
        }

        permission = cls.model(**permission_data)
        permission.save(force_insert=True)
        return permission

    @classmethod
    @DB.connection_context()
    def get_chat_permission(cls, user_id: str, tenant_id: str) -> Optional[ChatPermission]:
        """Get chat permission for user and tenant"""
        current_time = current_timestamp()

        try:
            return cls.model.select().where(
                (cls.model.user_id == user_id) &
                (cls.model.tenant_id == tenant_id) &
                (cls.model.status == StatusEnum.VALID.value) &
                ((cls.model.expires_at.is_null()) | (cls.model.expires_at > current_time))
            ).get()
        except peewee.DoesNotExist:
            return None

    @classmethod
    @DB.connection_context()
    def revoke_chat_permission(cls, user_id: str, tenant_id: str) -> bool:
        """Revoke chat permission"""
        try:
            cls.model.update({'status': StatusEnum.INVALID.value}).where(
                (cls.model.user_id == user_id) &
                (cls.model.tenant_id == tenant_id)
            ).execute()
            return True
        except Exception as e:
            logging.error(f"Failed to revoke chat permission for user {user_id} in tenant {tenant_id}: {e}")
            return False

    @classmethod
    @DB.connection_context()
    def user_has_chat_access(cls, user_id: str, tenant_id: str) -> bool:
        """Check if user has chat access for tenant"""
        return cls.get_chat_permission(user_id, tenant_id) is not None

    @classmethod
    @DB.connection_context()
    def get_user_chat_permissions(cls, user_id: str) -> List[Dict]:
        """Get all chat permissions for a user"""
        current_time = current_timestamp()

        query = (cls.model
                .select(cls.model, Tenant)
                .join(Tenant, on=(cls.model.tenant_id == Tenant.id))
                .where(
                    (cls.model.user_id == user_id) &
                    (cls.model.status == StatusEnum.VALID.value) &
                    (Tenant.status == StatusEnum.VALID.value) &
                    ((cls.model.expires_at.is_null()) | (cls.model.expires_at > current_time))
                ))

        permissions = []
        for perm in query:
            permissions.append({
                'permission_id': perm.id,
                'tenant_id': perm.tenant_id,
                'tenant_name': perm.tenant.name,
                'granted_at': perm.granted_at,
                'expires_at': perm.expires_at
            })

        return permissions
