#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import logging

from api.db import RB<PERSON><PERSON>oleCode, ScopeType, StatusEnum
from api.db.db_models import DB, User
from api.db.services.rbac_service import RBACService, UserRoleService


def init_super_admin():
    """Initialize Super Admin account if none exists"""
    try:
        with DB.connection_context():
            # Check if Super Admin already exists
            existing_super_admins = UserRoleService.get_users_with_role(
                RBACRoleCode.SUPER_ADMIN, ScopeType.SYSTEM
            )
            
            if existing_super_admins:
                logging.info(f"Super Admin already exists: {existing_super_admins[0]['email']}")
                return existing_super_admins[0]
            
            # Check if user with is_super_admin flag exists
            try:
                existing_user = User.select().where(
                    (User.is_super_admin == True) & 
                    (User.status == StatusEnum.VALID.value)
                ).get()
                
                logging.info(f"Found existing Super Admin user: {existing_user.email}")
                
                # Assign Super Admin role to existing user
                super_admin_role = RBACService.get_role_by_code(RBACRoleCode.SUPER_ADMIN)
                if super_admin_role:
                    UserRoleService.assign_role(
                        user_id=existing_user.id,
                        role_id=super_admin_role.id,
                        scope_type=ScopeType.SYSTEM,
                        granted_by=existing_user.id  # Self-granted for initial setup
                    )
                
                return {
                    'user_id': existing_user.id,
                    'email': existing_user.email,
                    'existing': True
                }
                
            except User.DoesNotExist:
                pass
            
            # Skip automatic Super Admin creation
            # Users should register through the frontend and then be promoted to Super Admin
            logging.info("No Super Admin found. Use the promotion endpoint to create one.")
            return None
            
    except Exception as e:
        logging.error(f"Failed to initialize Super Admin: {e}")
        raise


def init_rbac_system():
    """Initialize the complete RBAC system"""
    try:
        with DB.connection_context():
            logging.info("Initializing RBAC system...")
            
            # 1. Create default roles
            logging.info("Creating default RBAC roles...")
            RBACService.create_default_roles()
            
            # 2. Initialize Super Admin
            logging.info("Initializing Super Admin...")
            super_admin_info = init_super_admin()
            
            # 3. Log completion
            logging.info("RBAC system initialization completed successfully")
            
            return {
                'success': True,
                'super_admin': super_admin_info,
                'message': 'RBAC system initialized successfully'
            }
            
    except Exception as e:
        logging.error(f"RBAC system initialization failed: {e}")
        return {
            'success': False,
            'error': str(e),
            'message': 'RBAC system initialization failed'
        }


def migrate_existing_users():
    """Migrate existing users to RBAC system (optional)"""
    try:
        with DB.connection_context():
            logging.info("Starting user migration to RBAC system...")
            
            # Get all existing users without RBAC roles
            users_to_migrate = []
            all_users = User.select().where(User.status == StatusEnum.VALID.value)
            
            for user in all_users:
                user_roles = UserRoleService.get_user_roles(user.id)
                if not user_roles and not user.is_super_admin:
                    users_to_migrate.append(user)
            
            if not users_to_migrate:
                logging.info("No users need migration")
                return {'migrated': 0, 'message': 'No users to migrate'}
            
            # Assign basic user role to users without roles
            basic_user_role = RBACService.get_role_by_code(RBACRoleCode.BASIC_USER)
            if not basic_user_role:
                raise Exception("Basic User role not found")
            
            migrated_count = 0
            for user in users_to_migrate:
                try:
                    # Assign to system scope initially - Super Admin can reassign later
                    UserRoleService.assign_role(
                        user_id=user.id,
                        role_id=basic_user_role.id,
                        scope_type=ScopeType.SYSTEM,
                        granted_by=None  # System migration
                    )
                    migrated_count += 1
                except Exception as e:
                    logging.warning(f"Failed to migrate user {user.email}: {e}")
            
            logging.info(f"Migrated {migrated_count} users to RBAC system")
            return {
                'migrated': migrated_count,
                'total': len(users_to_migrate),
                'message': f'Migrated {migrated_count} users successfully'
            }
            
    except Exception as e:
        logging.error(f"User migration failed: {e}")
        return {
            'migrated': 0,
            'error': str(e),
            'message': 'User migration failed'
        }
