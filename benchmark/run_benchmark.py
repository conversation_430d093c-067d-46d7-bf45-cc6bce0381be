#!/usr/bin/env python3
"""
Simple runner script for the Excel Parser Benchmark.
This script provides an easy way to run the benchmark with common configurations.
"""

import sys
import subprocess
from pathlib import Path

def install_requirements():
    """Install required packages for the benchmark."""
    requirements_file = Path(__file__).parent / "requirements.txt"
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ])
        print("✅ Requirements installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def run_quick_benchmark():
    """Run a quick benchmark with small files only."""
    from excel_parser_benchmark import ExcelParserBenchmark
    
    print("🚀 Running Quick Benchmark (small files only)...")
    benchmark = ExcelParserBenchmark()
    
    # Generate only small test files
    benchmark.generate_test_files()
    
    # Filter to exclude large file
    small_files = [f.name for f in benchmark.test_files if "large_file" not in f.name]
    
    benchmark.run_benchmark(test_specific_files=small_files)
    print("✅ Quick benchmark completed!")

def run_full_benchmark():
    """Run the complete benchmark suite."""
    from excel_parser_benchmark import ExcelParserBenchmark
    
    print("🚀 Running Full Benchmark Suite...")
    benchmark = ExcelParserBenchmark()
    benchmark.run_benchmark()
    print("✅ Full benchmark completed!")

def main():
    """Main function with user interaction."""
    print("📊 Excel Parser Benchmark Tool")
    print("=" * 40)
    
    # Check if requirements are installed
    try:
        import pandas
        import openpyxl
        import psutil
        print("✅ Core requirements are available")
    except ImportError as e:
        print(f"❌ Missing requirements: {e}")
        print("Installing requirements...")
        if not install_requirements():
            return
    
    # Check Docling availability
    try:
        import docling
        print("✅ Docling is available")
    except ImportError:
        print("⚠️  Docling not available - will be skipped in benchmark")
        response = input("Would you like to install Docling? (y/n): ").lower()
        if response == 'y':
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "docling"])
                print("✅ Docling installed successfully!")
            except subprocess.CalledProcessError:
                print("❌ Failed to install Docling - continuing without it")
    
    print("\nBenchmark Options:")
    print("1. Quick benchmark (excludes large files, ~5 minutes)")
    print("2. Full benchmark (includes all files, ~15-30 minutes)")
    print("3. Exit")
    
    while True:
        choice = input("\nSelect option (1-3): ").strip()
        
        if choice == "1":
            run_quick_benchmark()
            break
        elif choice == "2":
            run_full_benchmark()
            break
        elif choice == "3":
            print("Goodbye!")
            break
        else:
            print("Invalid choice. Please select 1, 2, or 3.")

if __name__ == "__main__":
    main()
