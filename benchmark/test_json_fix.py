#!/usr/bin/env python3
"""
Test script to verify the JSON serialization fix works correctly.
This script tests the convert_numpy_types function and creates a minimal benchmark
to ensure JSON serialization works without errors.
"""

import sys
import json
import tempfile
from pathlib import Path
import numpy as np
import pandas as pd

# Add RAGFlow to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_convert_numpy_types():
    """Test the convert_numpy_types function with various numpy types."""
    print("🧪 Testing convert_numpy_types function...")
    
    from excel_parser_benchmark import convert_numpy_types
    
    # Test data with various numpy types
    test_data = {
        'int64_value': np.int64(42),
        'float64_value': np.float64(3.14159),
        'int32_value': np.int32(100),
        'float32_value': np.float32(2.718),
        'numpy_array': np.array([1, 2, 3, 4, 5]),
        'nested_dict': {
            'inner_int64': np.int64(999),
            'inner_float64': np.float64(1.414),
            'inner_list': [np.int64(1), np.int64(2), np.int64(3)]
        },
        'list_with_numpy': [np.int64(10), np.float64(20.5), np.int64(30)],
        'regular_int': 42,
        'regular_float': 3.14,
        'regular_string': "hello",
        'none_value': None,
        'boolean_value': True
    }
    
    # Convert numpy types
    converted_data = convert_numpy_types(test_data)
    
    # Verify types are converted correctly
    assert isinstance(converted_data['int64_value'], int)
    assert isinstance(converted_data['float64_value'], float)
    assert isinstance(converted_data['int32_value'], int)
    assert isinstance(converted_data['float32_value'], float)
    assert isinstance(converted_data['numpy_array'], list)
    assert isinstance(converted_data['nested_dict']['inner_int64'], int)
    assert isinstance(converted_data['nested_dict']['inner_float64'], float)
    assert isinstance(converted_data['nested_dict']['inner_list'][0], int)
    assert isinstance(converted_data['list_with_numpy'][0], int)
    assert isinstance(converted_data['list_with_numpy'][1], float)
    
    # Verify values are preserved
    assert converted_data['int64_value'] == 42
    assert abs(converted_data['float64_value'] - 3.14159) < 1e-10
    assert converted_data['numpy_array'] == [1, 2, 3, 4, 5]
    assert converted_data['regular_int'] == 42
    assert converted_data['regular_string'] == "hello"
    assert converted_data['none_value'] is None
    assert converted_data['boolean_value'] is True
    
    print("✅ convert_numpy_types function works correctly")
    return True

def test_json_serialization():
    """Test that converted data can be serialized to JSON."""
    print("🧪 Testing JSON serialization...")
    
    from excel_parser_benchmark import convert_numpy_types
    
    # Create test data with pandas DataFrame (which produces numpy types)
    df = pd.DataFrame({
        'A': [1, 2, 3],
        'B': [1.1, 2.2, 3.3],
        'C': ['x', 'y', 'z']
    })
    
    # Simulate benchmark result data
    test_result = {
        'parser_name': 'Test_Parser',
        'file_name': 'test.xlsx',
        'total_rows': len(df),  # This will be numpy.int64
        'total_columns': len(df.columns),  # This will be numpy.int64
        'memory_usage': df.memory_usage(deep=True).sum(),  # This will be numpy.int64
        'data_types': {str(dtype): int(count) for dtype, count in df.dtypes.value_counts().items()},
        'file_size_mb': np.float64(2.5),
        'parsing_time': np.float64(1.23)
    }
    
    print(f"Original types: {type(test_result['total_rows'])}, {type(test_result['memory_usage'])}")
    
    # Convert numpy types
    converted_result = convert_numpy_types(test_result)
    
    print(f"Converted types: {type(converted_result['total_rows'])}, {type(converted_result['memory_usage'])}")
    
    # Try to serialize to JSON
    try:
        json_str = json.dumps(converted_result, indent=2)
        print("✅ JSON serialization successful")
        
        # Verify we can parse it back
        parsed_data = json.loads(json_str)
        assert parsed_data['total_rows'] == 3
        assert parsed_data['total_columns'] == 3
        print("✅ JSON round-trip successful")
        
        return True
        
    except TypeError as e:
        print(f"❌ JSON serialization failed: {e}")
        return False

def test_minimal_benchmark():
    """Test a minimal benchmark to ensure the fix works in practice."""
    print("🧪 Testing minimal benchmark...")
    
    try:
        from excel_parser_benchmark import ExcelParserBenchmark
        
        # Create temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Initialize benchmark
            benchmark = ExcelParserBenchmark(output_dir=temp_path / "test_results")
            
            # Generate just one small test file
            benchmark._create_simple_table(temp_path / "test_simple.xlsx")
            
            # Create a test file entry
            from excel_parser_benchmark import TestFile
            test_file = TestFile(
                name="test_simple.xlsx",
                path=temp_path / "test_simple.xlsx",
                description="Test file for JSON serialization",
                expected_rows=10,
                expected_columns=6,
                expected_sheets=1
            )
            
            # Test RAGFlow Table parser (most likely to produce numpy types)
            try:
                result = benchmark._benchmark_ragflow_table(test_file)
                print(f"✅ Table parser test successful: {result.success}")
                
                # Add result to benchmark
                benchmark.results = [result]
                
                # Test report generation (this is where JSON serialization happens)
                benchmark._generate_reports()
                
                # Verify JSON file was created and is valid
                json_file = benchmark.output_dir / "benchmark_results.json"
                if json_file.exists():
                    with open(json_file, 'r') as f:
                        data = json.load(f)
                    print("✅ JSON report generated successfully")
                    print(f"   Report contains {len(data)} results")
                    return True
                else:
                    print("❌ JSON report file not created")
                    return False
                    
            except Exception as e:
                print(f"❌ Benchmark test failed: {e}")
                return False
                
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing JSON Serialization Fix")
    print("=" * 40)
    
    tests = [
        ("Convert Numpy Types", test_convert_numpy_types),
        ("JSON Serialization", test_json_serialization),
        ("Minimal Benchmark", test_minimal_benchmark)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! JSON serialization fix is working correctly.")
        print("\nThe benchmark should now run without JSON serialization errors.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
