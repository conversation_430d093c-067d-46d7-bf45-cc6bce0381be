#!/usr/bin/env python3
"""
Integration Example: Smart Excel Parser Router

This example shows how to integrate the benchmark findings into RAGFlow
to automatically choose the optimal Excel parsing method based on file characteristics.
"""

import os
import sys
from pathlib import Path
from typing import Union, Dict, Any, Optional
import logging

# Add RAGFlow to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from deepdoc.parser.excel_parser import RAGFlowExcelParser
from rag.app.table import Excel as TableExcel
from io import BytesIO
import pandas as pd
from openpyxl import load_workbook


class SmartExcelParser:
    """
    Smart Excel parser that automatically chooses the optimal parsing method
    based on file characteristics and benchmark findings.
    """
    
    def __init__(self, 
                 size_threshold_mb: float = 10.0,
                 table_detection_threshold: float = 0.8,
                 enable_docling: bool = False):
        """
        Initialize the smart parser.
        
        Args:
            size_threshold_mb: File size threshold for choosing parsing method
            table_detection_threshold: Threshold for detecting tabular structure
            enable_docling: Whether to use Docling for complex documents
        """
        self.size_threshold_mb = size_threshold_mb
        self.table_detection_threshold = table_detection_threshold
        self.enable_docling = enable_docling
        
        # Initialize parsers
        self.naive_parser = RAGFlowExcelParser()
        self.table_parser = TableExcel()
        
        if enable_docling:
            try:
                from docling.document_converter import DocumentConverter
                self.docling_converter = DocumentConverter()
            except ImportError:
                logging.warning("Docling not available, falling back to RAGFlow parsers")
                self.enable_docling = False
        
        self.logger = logging.getLogger(__name__)
    
    def analyze_file_structure(self, file_path: Union[str, Path], binary_data: Optional[bytes] = None) -> Dict[str, Any]:
        """
        Analyze Excel file structure to determine optimal parsing method.
        
        Returns:
            Dictionary with file analysis results
        """
        analysis = {
            'file_size_mb': 0,
            'total_sheets': 0,
            'max_rows': 0,
            'max_columns': 0,
            'has_formulas': False,
            'has_merged_cells': False,
            'table_structure_score': 0.0,
            'recommended_parser': 'naive'
        }
        
        try:
            # Get file size
            if binary_data:
                analysis['file_size_mb'] = len(binary_data) / 1024 / 1024
                wb = load_workbook(BytesIO(binary_data), data_only=False)
            else:
                analysis['file_size_mb'] = Path(file_path).stat().st_size / 1024 / 1024
                wb = load_workbook(file_path, data_only=False)
            
            analysis['total_sheets'] = len(wb.sheetnames)
            
            # Analyze each sheet
            total_cells = 0
            table_like_cells = 0
            
            for sheet_name in wb.sheetnames:
                ws = wb[sheet_name]
                rows = list(ws.rows)
                
                if not rows:
                    continue
                
                analysis['max_rows'] = max(analysis['max_rows'], len(rows))
                analysis['max_columns'] = max(analysis['max_columns'], len(rows[0]) if rows else 0)
                
                # Check for formulas
                for row in rows:
                    for cell in row:
                        if cell.value is not None:
                            total_cells += 1
                            
                            # Check for formulas
                            if hasattr(cell, 'data_type') and cell.data_type == 'f':
                                analysis['has_formulas'] = True
                            
                            # Check for merged cells
                            if ws.merged_cells:
                                analysis['has_merged_cells'] = True
                
                # Analyze table structure
                if len(rows) > 1:
                    # Check if first row looks like headers
                    first_row = [cell.value for cell in rows[0] if cell.value is not None]
                    if len(first_row) > 1:
                        # Check if subsequent rows have consistent structure
                        consistent_rows = 0
                        for row in rows[1:6]:  # Check first 5 data rows
                            non_empty_cells = sum(1 for cell in row if cell.value is not None)
                            if non_empty_cells >= len(first_row) * 0.5:  # At least 50% filled
                                consistent_rows += 1
                                table_like_cells += non_empty_cells
                        
                        if consistent_rows >= 3:  # At least 3 consistent rows
                            analysis['table_structure_score'] += 0.3
            
            # Calculate overall table structure score
            if total_cells > 0:
                analysis['table_structure_score'] = min(1.0, table_like_cells / total_cells)
            
            # Determine recommended parser based on analysis
            analysis['recommended_parser'] = self._determine_optimal_parser(analysis)
            
        except Exception as e:
            self.logger.error(f"Error analyzing file structure: {e}")
            analysis['recommended_parser'] = 'naive'  # Safe fallback
        
        return analysis
    
    def _determine_optimal_parser(self, analysis: Dict[str, Any]) -> str:
        """
        Determine the optimal parser based on file analysis and benchmark findings.
        """
        # Large files: prefer table parser for efficiency (if structured)
        if analysis['file_size_mb'] > self.size_threshold_mb:
            if analysis['table_structure_score'] > self.table_detection_threshold:
                return 'table'
            else:
                return 'naive'  # Large but not tabular
        
        # High table structure score: use table parser
        if analysis['table_structure_score'] > self.table_detection_threshold:
            return 'table'
        
        # Complex layouts with merged cells: consider docling or naive
        if analysis['has_merged_cells'] and self.enable_docling:
            return 'docling'
        
        # Default to naive for mixed content
        return 'naive'
    
    def parse(self, 
              file_path: Union[str, Path], 
              binary_data: Optional[bytes] = None,
              force_parser: Optional[str] = None,
              callback=None) -> Dict[str, Any]:
        """
        Parse Excel file using the optimal method.
        
        Args:
            file_path: Path to Excel file
            binary_data: Binary data of the file (optional)
            force_parser: Force specific parser ('naive', 'table', 'docling')
            callback: Progress callback function
        
        Returns:
            Dictionary with parsing results and metadata
        """
        # Analyze file if parser not forced
        if force_parser:
            parser_choice = force_parser
            analysis = {'recommended_parser': force_parser}
        else:
            analysis = self.analyze_file_structure(file_path, binary_data)
            parser_choice = analysis['recommended_parser']
        
        self.logger.info(f"Using {parser_choice} parser for {file_path}")
        
        # Parse with chosen method
        try:
            if parser_choice == 'table':
                result = self._parse_with_table(file_path, binary_data, callback)
            elif parser_choice == 'docling' and self.enable_docling:
                result = self._parse_with_docling(file_path, binary_data, callback)
            else:  # naive
                result = self._parse_with_naive(file_path, binary_data, callback)
            
            return {
                'success': True,
                'parser_used': parser_choice,
                'file_analysis': analysis,
                'data': result,
                'error': None
            }
            
        except Exception as e:
            self.logger.error(f"Parsing failed with {parser_choice}: {e}")
            
            # Fallback to naive parser if other methods fail
            if parser_choice != 'naive':
                try:
                    self.logger.info("Falling back to naive parser")
                    result = self._parse_with_naive(file_path, binary_data, callback)
                    return {
                        'success': True,
                        'parser_used': 'naive_fallback',
                        'file_analysis': analysis,
                        'data': result,
                        'error': f"Primary parser ({parser_choice}) failed: {e}"
                    }
                except Exception as fallback_error:
                    return {
                        'success': False,
                        'parser_used': parser_choice,
                        'file_analysis': analysis,
                        'data': None,
                        'error': f"All parsers failed. Primary: {e}, Fallback: {fallback_error}"
                    }
            else:
                return {
                    'success': False,
                    'parser_used': parser_choice,
                    'file_analysis': analysis,
                    'data': None,
                    'error': str(e)
                }
    
    def _parse_with_naive(self, file_path, binary_data, callback):
        """Parse with RAGFlow naive method."""
        if binary_data:
            return self.naive_parser(binary_data)
        else:
            with open(file_path, 'rb') as f:
                return self.naive_parser(f.read())
    
    def _parse_with_table(self, file_path, binary_data, callback):
        """Parse with RAGFlow table method."""
        def dummy_callback(progress, message=""):
            if callback:
                callback(progress, message)
        
        return self.table_parser(str(file_path), binary=binary_data, callback=dummy_callback)
    
    def _parse_with_docling(self, file_path, binary_data, callback):
        """Parse with Docling method."""
        if binary_data:
            # Save to temporary file for Docling
            import tempfile
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
                tmp.write(binary_data)
                tmp_path = tmp.name
            
            try:
                result = self.docling_converter.convert(tmp_path)
                return result.document.export_to_markdown()
            finally:
                os.unlink(tmp_path)
        else:
            result = self.docling_converter.convert(file_path)
            return result.document.export_to_markdown()


def example_usage():
    """Example of how to use the SmartExcelParser."""
    
    # Initialize smart parser
    parser = SmartExcelParser(
        size_threshold_mb=5.0,  # Use table parser for files > 5MB
        table_detection_threshold=0.7,  # 70% table structure confidence
        enable_docling=True  # Enable Docling for complex layouts
    )
    
    # Example file paths (you would use real files)
    test_files = [
        "test_data/simple_table.xlsx",
        "test_data/complex_formatted.xlsx", 
        "test_data/large_file.xlsx"
    ]
    
    for file_path in test_files:
        if not Path(file_path).exists():
            print(f"Skipping {file_path} (file not found)")
            continue
        
        print(f"\nProcessing: {file_path}")
        
        # Parse with automatic method selection
        result = parser.parse(file_path)
        
        if result['success']:
            print(f"✅ Success with {result['parser_used']} parser")
            print(f"File analysis: {result['file_analysis']}")
            print(f"Data type: {type(result['data'])}")
            
            if isinstance(result['data'], list) and result['data']:
                if isinstance(result['data'][0], pd.DataFrame):
                    print(f"Extracted {len(result['data'])} DataFrames")
                    total_rows = sum(len(df) for df in result['data'])
                    print(f"Total rows: {total_rows}")
                else:
                    print(f"Extracted {len(result['data'])} text chunks")
        else:
            print(f"❌ Failed: {result['error']}")


if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Run example
    example_usage()
