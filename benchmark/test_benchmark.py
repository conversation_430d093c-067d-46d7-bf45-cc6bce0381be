#!/usr/bin/env python3
"""
Test script to verify the benchmark implementation works correctly.
This script runs a minimal test to ensure all components are functioning.
"""

import sys
import tempfile
import shutil
from pathlib import Path

# Add RAGFlow to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_benchmark_basic():
    """Test basic benchmark functionality."""
    print("🧪 Testing Excel Parser Benchmark...")
    
    try:
        from excel_parser_benchmark import ExcelParserBenchmark, TestFile, BenchmarkResult
        print("✅ Imports successful")
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    
    # Create temporary directory for test
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        try:
            # Initialize benchmark
            benchmark = ExcelParserBenchmark(output_dir=temp_path / "test_results")
            print("✅ Benchmark initialization successful")
            
            # Test file generation
            benchmark.generate_test_files()
            print(f"✅ Generated {len(benchmark.test_files)} test files")
            
            # Verify test files exist
            for test_file in benchmark.test_files:
                if not test_file.path.exists():
                    print(f"❌ Test file not created: {test_file.path}")
                    return False
            print("✅ All test files created successfully")
            
            # Test individual parser methods
            if benchmark.test_files:
                test_file = benchmark.test_files[0]  # Use first (smallest) file
                
                # Test RAGFlow Naive parser
                try:
                    result = benchmark._benchmark_ragflow_naive(test_file)
                    if result.success:
                        print("✅ RAGFlow Naive parser test successful")
                    else:
                        print(f"⚠️  RAGFlow Naive parser failed: {result.error_message}")
                except Exception as e:
                    print(f"❌ RAGFlow Naive parser test failed: {e}")
                
                # Test RAGFlow Table parser
                try:
                    result = benchmark._benchmark_ragflow_table(test_file)
                    if result.success:
                        print("✅ RAGFlow Table parser test successful")
                    else:
                        print(f"⚠️  RAGFlow Table parser failed: {result.error_message}")
                except Exception as e:
                    print(f"❌ RAGFlow Table parser test failed: {e}")
                
                # Test Docling parser (if available)
                try:
                    result = benchmark._benchmark_docling(test_file)
                    if result.success:
                        print("✅ Docling parser test successful")
                    else:
                        print(f"⚠️  Docling parser failed: {result.error_message}")
                except Exception as e:
                    print(f"❌ Docling parser test failed: {e}")
            
            print("✅ Basic benchmark test completed successfully")
            return True
            
        except Exception as e:
            print(f"❌ Benchmark test failed: {e}")
            return False

def test_integration_example():
    """Test the integration example."""
    print("\n🧪 Testing Integration Example...")
    
    try:
        from integration_example import SmartExcelParser
        print("✅ Integration example imports successful")
        
        # Initialize smart parser
        parser = SmartExcelParser(enable_docling=False)  # Disable Docling for test
        print("✅ SmartExcelParser initialization successful")
        
        # Test file analysis (without actual file)
        analysis = {
            'file_size_mb': 2.5,
            'total_sheets': 1,
            'max_rows': 1000,
            'max_columns': 6,
            'has_formulas': False,
            'has_merged_cells': False,
            'table_structure_score': 0.9
        }
        
        recommended = parser._determine_optimal_parser(analysis)
        print(f"✅ Parser recommendation test successful: {recommended}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Integration example import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Integration example test failed: {e}")
        return False

def test_requirements():
    """Test if required packages are available."""
    print("\n🧪 Testing Requirements...")
    
    required_packages = [
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('openpyxl', 'openpyxl'),
        ('psutil', 'psutil'),
    ]
    
    optional_packages = [
        ('matplotlib', 'matplotlib'),
        ('seaborn', 'seaborn'),
        ('docling', 'docling'),
    ]
    
    all_good = True
    
    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"✅ {package_name} available")
        except ImportError:
            print(f"❌ {package_name} missing (required)")
            all_good = False
    
    for package_name, import_name in optional_packages:
        try:
            __import__(import_name)
            print(f"✅ {package_name} available")
        except ImportError:
            print(f"⚠️  {package_name} missing (optional)")
    
    return all_good

def main():
    """Run all tests."""
    print("🚀 Excel Parser Benchmark Test Suite")
    print("=" * 50)
    
    # Test requirements
    if not test_requirements():
        print("\n❌ Some required packages are missing.")
        print("Install with: pip install -r requirements.txt")
        return False
    
    # Test basic benchmark functionality
    if not test_benchmark_basic():
        print("\n❌ Basic benchmark test failed.")
        return False
    
    # Test integration example
    if not test_integration_example():
        print("\n❌ Integration example test failed.")
        return False
    
    print("\n🎉 All tests passed! The benchmark is ready to use.")
    print("\nNext steps:")
    print("1. Run quick benchmark: python run_benchmark.py")
    print("2. Run full benchmark: python excel_parser_benchmark.py")
    print("3. Check the README.md for detailed usage instructions")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
