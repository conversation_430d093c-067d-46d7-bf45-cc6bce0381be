#!/usr/bin/env python3
"""
Comprehensive Excel Parser Benchmark
Compares RAGFlow's current Excel parsing implementation against IBM's Docling parser.

This benchmark evaluates:
1. Current RAGFlowExcelParser (naive chunking method)
2. Current Table chunking method from rag/app/table.py
3. Docling parser implementation

Metrics evaluated:
- Performance: Parsing speed, memory usage, file size handling
- Data integrity: Accuracy of data extraction, handling of formulas, multiple sheets, data types
- Feature completeness: Support for complex Excel features
- Output quality: Structured data preservation, text extraction quality
"""

import asyncio
import gc
import logging
import os
import psutil
import sys
import time
import tracemalloc
from dataclasses import dataclass, field
from io import BytesIO
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union
from concurrent.futures import ThreadPoolExecutor
import json
import numpy as np

# Add the project root to the path to import RAGFlow modules
sys.path.insert(0, str(Path(__file__).parent.parent))

# RAGFlow imports
from deepdoc.parser.excel_parser import RAG<PERSON>lowExcelParser
from rag.app.table import Excel as TableExcel, chunk as table_chunk
from rag.app.naive import chunk as naive_chunk

# Third-party imports
import pandas as pd
import numpy as np
from openpyxl import Workbook, load_workbook

# Docling imports (will be installed if not available)
try:
    from docling.document_converter import DocumentConverter
    from docling.datamodel.base_models import InputFormat
    DOCLING_AVAILABLE = True
except ImportError:
    DOCLING_AVAILABLE = False
    print("Warning: Docling not available. Install with: pip install docling")


@dataclass
class BenchmarkResult:
    """Results from a single benchmark run."""
    parser_name: str
    file_name: str
    file_size_mb: float
    parsing_time_seconds: float
    memory_peak_mb: float
    memory_delta_mb: float
    success: bool
    error_message: Optional[str] = None
    
    # Data integrity metrics
    total_rows: int = 0
    total_columns: int = 0
    sheets_count: int = 0
    data_types_detected: Dict[str, int] = field(default_factory=dict)
    
    # Output quality metrics
    output_size_chars: int = 0
    structured_data_preserved: bool = False
    formulas_handled: bool = False
    multiple_sheets_handled: bool = False
    
    # Feature completeness
    supports_merged_cells: bool = False
    supports_formatting: bool = False
    supports_charts: bool = False


@dataclass
class TestFile:
    """Test file configuration."""
    name: str
    path: Path
    description: str
    expected_rows: int
    expected_columns: int
    expected_sheets: int
    has_formulas: bool = False
    has_merged_cells: bool = False
    has_formatting: bool = False
    has_charts: bool = False


def convert_numpy_types(obj):
    """
    Recursively convert numpy types to native Python types for JSON serialization.

    Args:
        obj: Object that may contain numpy types

    Returns:
        Object with numpy types converted to native Python types
    """
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(convert_numpy_types(item) for item in obj)
    elif hasattr(obj, 'item'):  # Handle numpy scalars
        return obj.item()
    else:
        return obj


class ExcelParserBenchmark:
    """Main benchmark class for comparing Excel parsers."""

    def __init__(self, output_dir: Path = Path("benchmark_results")):
        self.output_dir = output_dir
        self.output_dir.mkdir(exist_ok=True)
        self.results: List[BenchmarkResult] = []
        self.test_files: List[TestFile] = []

        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.output_dir / "benchmark.log"),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def generate_test_files(self) -> None:
        """Generate diverse test Excel files for benchmarking."""
        test_data_dir = self.output_dir / "test_data"
        test_data_dir.mkdir(exist_ok=True)
        
        # 1. Simple tabular data (ideal for Table chunking)
        self._create_simple_table(test_data_dir / "simple_table.xlsx")
        
        # 2. Complex formatted document (better for naive chunking)
        self._create_complex_formatted(test_data_dir / "complex_formatted.xlsx")
        
        # 3. Large file (>10MB) for scalability testing
        self._create_large_file(test_data_dir / "large_file.xlsx")
        
        # 4. Multiple sheets with mixed content
        self._create_multi_sheet(test_data_dir / "multi_sheet.xlsx")
        
        # 5. File with formulas and data types
        self._create_formulas_file(test_data_dir / "formulas_datatypes.xlsx")
        
        # 6. File with merged cells and formatting
        self._create_merged_cells(test_data_dir / "merged_cells.xlsx")
        
        self.logger.info(f"Generated {len(self.test_files)} test files")
    
    def _create_simple_table(self, filepath: Path) -> None:
        """Create a simple tabular data file."""
        wb = Workbook()
        ws = wb.active
        ws.title = "Employee_Data"
        
        # Headers
        headers = ["ID", "Name", "Department", "Salary", "Join_Date", "Active"]
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        
        # Data (1000 rows)
        for row in range(2, 1002):
            ws.cell(row=row, column=1, value=row-1)  # ID
            ws.cell(row=row, column=2, value=f"Employee_{row-1}")  # Name
            ws.cell(row=row, column=3, value=f"Dept_{(row-1) % 10}")  # Department
            ws.cell(row=row, column=4, value=50000 + (row-1) * 100)  # Salary
            ws.cell(row=row, column=5, value=f"2020-{(row-1) % 12 + 1:02d}-01")  # Join_Date
            ws.cell(row=row, column=6, value=(row-1) % 2 == 0)  # Active
        
        wb.save(filepath)
        
        self.test_files.append(TestFile(
            name="simple_table.xlsx",
            path=filepath,
            description="Simple tabular data with 1000 rows, 6 columns",
            expected_rows=1001,
            expected_columns=6,
            expected_sheets=1
        ))
    
    def _create_complex_formatted(self, filepath: Path) -> None:
        """Create a complex formatted document."""
        wb = Workbook()
        ws = wb.active
        ws.title = "Report"
        
        # Title and description
        ws.cell(row=1, column=1, value="Monthly Sales Report")
        ws.cell(row=2, column=1, value="Generated on: 2024-01-15")
        ws.cell(row=3, column=1, value="")
        
        # Summary section
        ws.cell(row=4, column=1, value="Summary:")
        ws.cell(row=5, column=1, value="Total Sales: $125,000")
        ws.cell(row=6, column=1, value="Total Orders: 450")
        ws.cell(row=7, column=1, value="")
        
        # Data table
        headers = ["Product", "Units Sold", "Revenue", "Profit Margin"]
        for col, header in enumerate(headers, 1):
            ws.cell(row=8, column=col, value=header)
        
        # Sample data
        products = ["Product A", "Product B", "Product C", "Product D", "Product E"]
        for i, product in enumerate(products, 9):
            ws.cell(row=i, column=1, value=product)
            ws.cell(row=i, column=2, value=100 + i * 10)
            ws.cell(row=i, column=3, value=f"${(100 + i * 10) * 25}")
            ws.cell(row=i, column=4, value=f"{15 + i * 2}%")
        
        wb.save(filepath)
        
        self.test_files.append(TestFile(
            name="complex_formatted.xlsx",
            path=filepath,
            description="Complex formatted document with mixed content types",
            expected_rows=13,
            expected_columns=4,
            expected_sheets=1,
            has_formatting=True
        ))
    
    def _create_large_file(self, filepath: Path) -> None:
        """Create a large file (>10MB) for scalability testing."""
        wb = Workbook()
        ws = wb.active
        ws.title = "Large_Dataset"
        
        # Headers (20 columns)
        headers = [f"Column_{i}" for i in range(1, 21)]
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        
        # Data (50,000 rows to ensure >10MB)
        for row in range(2, 50002):
            for col in range(1, 21):
                if col <= 5:  # Text columns
                    ws.cell(row=row, column=col, value=f"Data_{row}_{col}")
                elif col <= 10:  # Numeric columns
                    ws.cell(row=row, column=col, value=row * col * 1.5)
                elif col <= 15:  # Date columns
                    ws.cell(row=row, column=col, value=f"2024-{(row % 12) + 1:02d}-{(col % 28) + 1:02d}")
                else:  # Boolean columns
                    ws.cell(row=row, column=col, value=(row + col) % 2 == 0)
        
        wb.save(filepath)
        
        self.test_files.append(TestFile(
            name="large_file.xlsx",
            path=filepath,
            description="Large file with 50,000 rows, 20 columns (>10MB)",
            expected_rows=50001,
            expected_columns=20,
            expected_sheets=1
        ))

    def _create_multi_sheet(self, filepath: Path) -> None:
        """Create a file with multiple sheets and mixed content."""
        wb = Workbook()

        # Sheet 1: Sales Data
        ws1 = wb.active
        ws1.title = "Sales_Data"
        headers1 = ["Date", "Product", "Quantity", "Price", "Total"]
        for col, header in enumerate(headers1, 1):
            ws1.cell(row=1, column=col, value=header)

        for row in range(2, 102):  # 100 rows
            ws1.cell(row=row, column=1, value=f"2024-01-{(row-1) % 31 + 1:02d}")
            ws1.cell(row=row, column=2, value=f"Product_{(row-1) % 5 + 1}")
            ws1.cell(row=row, column=3, value=(row-1) * 2)
            ws1.cell(row=row, column=4, value=25.50 + (row-1) * 0.1)
            ws1.cell(row=row, column=5, value=f"=C{row}*D{row}")  # Formula

        # Sheet 2: Customer Data
        ws2 = wb.create_sheet("Customer_Data")
        headers2 = ["Customer_ID", "Name", "Email", "Country", "Registration_Date"]
        for col, header in enumerate(headers2, 1):
            ws2.cell(row=1, column=col, value=header)

        for row in range(2, 52):  # 50 rows
            ws2.cell(row=row, column=1, value=f"CUST_{row-1:04d}")
            ws2.cell(row=row, column=2, value=f"Customer {row-1}")
            ws2.cell(row=row, column=3, value=f"customer{row-1}@example.com")
            ws2.cell(row=row, column=4, value=f"Country_{(row-1) % 10}")
            ws2.cell(row=row, column=5, value=f"2023-{(row-1) % 12 + 1:02d}-15")

        # Sheet 3: Summary
        ws3 = wb.create_sheet("Summary")
        ws3.cell(row=1, column=1, value="Business Summary Report")
        ws3.cell(row=2, column=1, value="")
        ws3.cell(row=3, column=1, value="Total Sales Records:")
        ws3.cell(row=3, column=2, value="=COUNTA(Sales_Data!A:A)-1")  # Formula
        ws3.cell(row=4, column=1, value="Total Customers:")
        ws3.cell(row=4, column=2, value="=COUNTA(Customer_Data!A:A)-1")  # Formula

        wb.save(filepath)

        self.test_files.append(TestFile(
            name="multi_sheet.xlsx",
            path=filepath,
            description="Multiple sheets with formulas and mixed content",
            expected_rows=153,  # Total across all sheets
            expected_columns=5,
            expected_sheets=3,
            has_formulas=True
        ))

    def _create_formulas_file(self, filepath: Path) -> None:
        """Create a file with various formulas and data types."""
        wb = Workbook()
        ws = wb.active
        ws.title = "Formulas_DataTypes"

        # Headers
        headers = ["Item", "Quantity", "Unit_Price", "Total", "Tax_Rate", "Tax_Amount", "Final_Total", "Date", "Active"]
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)

        # Data with formulas
        for row in range(2, 52):  # 50 rows
            ws.cell(row=row, column=1, value=f"Item_{row-1}")  # Text
            ws.cell(row=row, column=2, value=row-1)  # Integer
            ws.cell(row=row, column=3, value=10.50 + (row-1) * 0.25)  # Float
            ws.cell(row=row, column=4, value=f"=B{row}*C{row}")  # Formula: Quantity * Unit_Price
            ws.cell(row=row, column=5, value=0.08)  # Tax rate (8%)
            ws.cell(row=row, column=6, value=f"=D{row}*E{row}")  # Formula: Total * Tax_Rate
            ws.cell(row=row, column=7, value=f"=D{row}+F{row}")  # Formula: Total + Tax_Amount
            ws.cell(row=row, column=8, value=f"2024-01-{(row-1) % 31 + 1:02d}")  # Date
            ws.cell(row=row, column=9, value=(row-1) % 2 == 0)  # Boolean

        # Summary row with aggregate formulas
        ws.cell(row=52, column=1, value="TOTAL")
        ws.cell(row=52, column=2, value="=SUM(B2:B51)")  # Sum of quantities
        ws.cell(row=52, column=4, value="=SUM(D2:D51)")  # Sum of totals
        ws.cell(row=52, column=6, value="=SUM(F2:F51)")  # Sum of tax amounts
        ws.cell(row=52, column=7, value="=SUM(G2:G51)")  # Sum of final totals

        wb.save(filepath)

        self.test_files.append(TestFile(
            name="formulas_datatypes.xlsx",
            path=filepath,
            description="File with various formulas and data types",
            expected_rows=52,
            expected_columns=9,
            expected_sheets=1,
            has_formulas=True
        ))

    def _create_merged_cells(self, filepath: Path) -> None:
        """Create a file with merged cells and formatting."""
        wb = Workbook()
        ws = wb.active
        ws.title = "Merged_Cells"

        # Title with merged cells
        ws.merge_cells('A1:E1')
        ws.cell(row=1, column=1, value="Quarterly Sales Report")

        # Subtitle with merged cells
        ws.merge_cells('A2:E2')
        ws.cell(row=2, column=1, value="Q1 2024 Performance Summary")

        # Empty row
        ws.cell(row=3, column=1, value="")

        # Headers
        headers = ["Region", "Q1 Sales", "Q1 Target", "Achievement %", "Status"]
        for col, header in enumerate(headers, 1):
            ws.cell(row=4, column=col, value=header)

        # Data
        regions = ["North", "South", "East", "West", "Central"]
        for i, region in enumerate(regions, 5):
            ws.cell(row=i, column=1, value=region)
            sales = 100000 + i * 15000
            target = 120000
            ws.cell(row=i, column=2, value=sales)
            ws.cell(row=i, column=3, value=target)
            ws.cell(row=i, column=4, value=f"=B{i}/C{i}*100")  # Achievement percentage
            ws.cell(row=i, column=5, value=f"=IF(D{i}>=100,\"Met\",\"Below\")")  # Status formula

        # Total row with merged cells for label
        ws.merge_cells('A10:A11')
        ws.cell(row=10, column=1, value="TOTAL")
        ws.cell(row=10, column=2, value="=SUM(B5:B9)")
        ws.cell(row=10, column=3, value="=SUM(C5:C9)")
        ws.cell(row=10, column=4, value="=B10/C10*100")

        wb.save(filepath)

        self.test_files.append(TestFile(
            name="merged_cells.xlsx",
            path=filepath,
            description="File with merged cells and complex formatting",
            expected_rows=11,
            expected_columns=5,
            expected_sheets=1,
            has_merged_cells=True,
            has_formatting=True,
            has_formulas=True
        ))

    def _measure_memory_usage(self, func, *args, **kwargs) -> Tuple[Any, float, float]:
        """Measure memory usage of a function call."""
        # Start memory tracking
        tracemalloc.start()
        process = psutil.Process()
        memory_before = process.memory_info().rss / 1024 / 1024  # MB

        try:
            # Execute function
            result = func(*args, **kwargs)

            # Measure peak memory
            current, peak = tracemalloc.get_traced_memory()
            peak_mb = peak / 1024 / 1024  # Convert to MB

            # Measure memory after
            memory_after = process.memory_info().rss / 1024 / 1024  # MB
            memory_delta = memory_after - memory_before

            return result, peak_mb, memory_delta

        finally:
            tracemalloc.stop()
            gc.collect()  # Force garbage collection

    def _benchmark_ragflow_naive(self, test_file: TestFile) -> BenchmarkResult:
        """Benchmark RAGFlow's naive chunking method."""
        self.logger.info(f"Benchmarking RAGFlow Naive parser on {test_file.name}")

        try:
            # Read file
            with open(test_file.path, 'rb') as f:
                binary_data = f.read()

            file_size_mb = len(binary_data) / 1024 / 1024

            # Dummy callback for compatibility
            def dummy_callback(progress, message=""):
                pass

            # Measure parsing time and memory
            start_time = time.time()

            def parse_func():
                parser = RAGFlowExcelParser()
                if test_file.name.endswith('.xlsx') or test_file.name.endswith('.xls'):
                    return parser(binary_data)
                else:
                    return []

            result, peak_memory, memory_delta = self._measure_memory_usage(parse_func)
            parsing_time = time.time() - start_time

            # Analyze results
            total_rows = int(len(result)) if result else 0
            output_size = int(sum(len(str(item)) for item in result)) if result else 0

            # Try to detect sheets (RAGFlow naive doesn't preserve sheet info well)
            sheets_count = 1  # Assume single sheet for naive parsing

            return BenchmarkResult(
                parser_name="RAGFlow_Naive",
                file_name=test_file.name,
                file_size_mb=float(file_size_mb),
                parsing_time_seconds=float(parsing_time),
                memory_peak_mb=float(peak_memory),
                memory_delta_mb=float(memory_delta),
                success=True,
                total_rows=total_rows,
                total_columns=0,  # Not easily determinable from naive output
                sheets_count=sheets_count,
                output_size_chars=output_size,
                structured_data_preserved=False,  # Naive converts to text
                formulas_handled=True,  # Uses data_only=True
                multiple_sheets_handled=True,  # Processes all sheets
            )

        except Exception as e:
            self.logger.error(f"RAGFlow Naive parser failed on {test_file.name}: {str(e)}")
            return BenchmarkResult(
                parser_name="RAGFlow_Naive",
                file_name=test_file.name,
                file_size_mb=float(test_file.path.stat().st_size / 1024 / 1024),
                parsing_time_seconds=0.0,
                memory_peak_mb=0.0,
                memory_delta_mb=0.0,
                success=False,
                error_message=str(e)
            )

    def _benchmark_ragflow_table(self, test_file: TestFile) -> BenchmarkResult:
        """Benchmark RAGFlow's table chunking method."""
        self.logger.info(f"Benchmarking RAGFlow Table parser on {test_file.name}")

        try:
            # Read file
            with open(test_file.path, 'rb') as f:
                binary_data = f.read()

            file_size_mb = len(binary_data) / 1024 / 1024

            # Dummy callback for compatibility
            def dummy_callback(progress, message=""):
                pass

            # Measure parsing time and memory
            start_time = time.time()

            def parse_func():
                parser = TableExcel()
                return parser(test_file.name, binary=binary_data, callback=dummy_callback)

            result, peak_memory, memory_delta = self._measure_memory_usage(parse_func)
            parsing_time = time.time() - start_time

            # Analyze results (result is list of DataFrames)
            total_rows = int(sum(len(df) for df in result)) if result else 0
            total_columns = int(max(len(df.columns) for df in result)) if result else 0
            sheets_count = int(len(result)) if result else 0

            # Detect data types
            data_types = {}
            if result:
                for df in result:
                    for col in df.columns:
                        dtype = str(df[col].dtype)
                        data_types[dtype] = int(data_types.get(dtype, 0) + 1)

            # Calculate output size (approximate)
            output_size = 0
            if result:
                for df in result:
                    output_size += int(df.memory_usage(deep=True).sum())

            return BenchmarkResult(
                parser_name="RAGFlow_Table",
                file_name=test_file.name,
                file_size_mb=float(file_size_mb),
                parsing_time_seconds=float(parsing_time),
                memory_peak_mb=float(peak_memory),
                memory_delta_mb=float(memory_delta),
                success=True,
                total_rows=total_rows,
                total_columns=total_columns,
                sheets_count=sheets_count,
                data_types_detected=data_types,
                output_size_chars=output_size,
                structured_data_preserved=True,  # Table preserves structure
                formulas_handled=True,  # Converts formulas to values
                multiple_sheets_handled=True,  # Processes all sheets
            )

        except Exception as e:
            self.logger.error(f"RAGFlow Table parser failed on {test_file.name}: {str(e)}")
            return BenchmarkResult(
                parser_name="RAGFlow_Table",
                file_name=test_file.name,
                file_size_mb=float(test_file.path.stat().st_size / 1024 / 1024),
                parsing_time_seconds=0.0,
                memory_peak_mb=0.0,
                memory_delta_mb=0.0,
                success=False,
                error_message=str(e)
            )

    def _benchmark_docling(self, test_file: TestFile) -> BenchmarkResult:
        """Benchmark Docling parser."""
        if not DOCLING_AVAILABLE:
            return BenchmarkResult(
                parser_name="Docling",
                file_name=test_file.name,
                file_size_mb=float(test_file.path.stat().st_size / 1024 / 1024),
                parsing_time_seconds=0.0,
                memory_peak_mb=0.0,
                memory_delta_mb=0.0,
                success=False,
                error_message="Docling not available"
            )

        self.logger.info(f"Benchmarking Docling parser on {test_file.name}")

        try:
            file_size_mb = test_file.path.stat().st_size / 1024 / 1024

            # Measure parsing time and memory
            start_time = time.time()

            def parse_func():
                converter = DocumentConverter()
                result = converter.convert(test_file.path)
                return result.document

            document, peak_memory, memory_delta = self._measure_memory_usage(parse_func)
            parsing_time = time.time() - start_time

            # Analyze Docling document
            # Extract tables and text content
            tables = []
            text_content = ""

            try:
                # Get markdown output for analysis
                markdown_output = document.export_to_markdown()
                text_content = markdown_output

                # Try to extract table information from the document
                # Docling structures data differently, so we need to analyze the document structure
                if hasattr(document, 'tables') and document.tables:
                    tables = document.tables
                elif hasattr(document, 'body') and document.body:
                    # Count table-like structures in the body
                    for item in document.body:
                        if hasattr(item, 'text') and '|' in str(item.text):
                            # Rough heuristic for table detection
                            tables.append(item)

            except Exception as e:
                self.logger.warning(f"Error analyzing Docling output: {e}")
                text_content = str(document)

            # Estimate metrics
            total_rows = 0
            total_columns = 0
            sheets_count = 1  # Docling typically treats Excel as single document

            if tables:
                # Try to estimate from table structures
                for table in tables:
                    if hasattr(table, 'num_rows'):
                        total_rows += int(table.num_rows)
                    if hasattr(table, 'num_cols'):
                        total_columns = max(total_columns, int(table.num_cols))
            else:
                # Estimate from text content
                lines = text_content.split('\n')
                table_lines = [line for line in lines if '|' in line and line.count('|') > 1]
                if table_lines:
                    total_rows = int(len(table_lines) - 1)  # Subtract header
                    total_columns = int(max(line.count('|') - 1 for line in table_lines))

            return BenchmarkResult(
                parser_name="Docling",
                file_name=test_file.name,
                file_size_mb=float(file_size_mb),
                parsing_time_seconds=float(parsing_time),
                memory_peak_mb=float(peak_memory),
                memory_delta_mb=float(memory_delta),
                success=True,
                total_rows=int(total_rows),
                total_columns=int(total_columns),
                sheets_count=int(sheets_count),
                output_size_chars=int(len(text_content)),
                structured_data_preserved=len(tables) > 0,
                formulas_handled=True,  # Docling processes formulas
                multiple_sheets_handled=False,  # Docling treats as single document
                supports_merged_cells=True,  # Docling can handle complex layouts
                supports_formatting=True,  # Docling preserves some formatting
            )

        except Exception as e:
            self.logger.error(f"Docling parser failed on {test_file.name}: {str(e)}")
            return BenchmarkResult(
                parser_name="Docling",
                file_name=test_file.name,
                file_size_mb=float(test_file.path.stat().st_size / 1024 / 1024),
                parsing_time_seconds=0.0,
                memory_peak_mb=0.0,
                memory_delta_mb=0.0,
                success=False,
                error_message=str(e)
            )

    def run_benchmark(self, test_specific_files: Optional[List[str]] = None) -> None:
        """Run the complete benchmark suite."""
        self.logger.info("Starting Excel Parser Benchmark")

        # Generate test files if they don't exist
        if not self.test_files:
            self.generate_test_files()

        # Filter test files if specific files requested
        files_to_test = self.test_files
        if test_specific_files:
            files_to_test = [f for f in self.test_files if f.name in test_specific_files]

        # Run benchmarks for each parser and file combination
        parsers = [
            ("RAGFlow_Naive", self._benchmark_ragflow_naive),
            ("RAGFlow_Table", self._benchmark_ragflow_table),
            ("Docling", self._benchmark_docling),
        ]

        total_tests = len(files_to_test) * len(parsers)
        current_test = 0

        for test_file in files_to_test:
            self.logger.info(f"Testing file: {test_file.name} ({test_file.description})")

            for parser_name, parser_func in parsers:
                current_test += 1
                self.logger.info(f"Progress: {current_test}/{total_tests} - {parser_name}")

                try:
                    result = parser_func(test_file)
                    self.results.append(result)

                    if result.success:
                        self.logger.info(
                            f"{parser_name} completed in {result.parsing_time_seconds:.2f}s, "
                            f"Peak memory: {result.memory_peak_mb:.1f}MB"
                        )
                    else:
                        self.logger.warning(f"{parser_name} failed: {result.error_message}")

                except Exception as e:
                    self.logger.error(f"Unexpected error in {parser_name}: {str(e)}")
                    self.results.append(BenchmarkResult(
                        parser_name=parser_name,
                        file_name=test_file.name,
                        file_size_mb=test_file.path.stat().st_size / 1024 / 1024,
                        parsing_time_seconds=0,
                        memory_peak_mb=0,
                        memory_delta_mb=0,
                        success=False,
                        error_message=str(e)
                    ))

                # Small delay to allow system to stabilize
                time.sleep(1)

        self.logger.info("Benchmark completed. Generating reports...")
        self._generate_reports()

    def _generate_reports(self) -> None:
        """Generate comprehensive benchmark reports."""
        # Save raw results as JSON
        results_data = []
        for result in self.results:
            result_dict = {
                'parser_name': result.parser_name,
                'file_name': result.file_name,
                'file_size_mb': result.file_size_mb,
                'parsing_time_seconds': result.parsing_time_seconds,
                'memory_peak_mb': result.memory_peak_mb,
                'memory_delta_mb': result.memory_delta_mb,
                'success': result.success,
                'error_message': result.error_message,
                'total_rows': result.total_rows,
                'total_columns': result.total_columns,
                'sheets_count': result.sheets_count,
                'data_types_detected': result.data_types_detected,
                'output_size_chars': result.output_size_chars,
                'structured_data_preserved': result.structured_data_preserved,
                'formulas_handled': result.formulas_handled,
                'multiple_sheets_handled': result.multiple_sheets_handled,
                'supports_merged_cells': result.supports_merged_cells,
                'supports_formatting': result.supports_formatting,
                'supports_charts': result.supports_charts,
            }
            results_data.append(result_dict)

        # Convert any remaining numpy types to native Python types
        results_data = convert_numpy_types(results_data)

        with open(self.output_dir / "benchmark_results.json", 'w') as f:
            json.dump(results_data, f, indent=2)

        # Generate summary report
        self._generate_summary_report()

        # Generate detailed analysis
        self._generate_detailed_analysis()

        # Generate performance comparison
        self._generate_performance_comparison()

        self.logger.info(f"Reports generated in {self.output_dir}")

    def _generate_summary_report(self) -> None:
        """Generate a summary report."""
        with open(self.output_dir / "summary_report.md", 'w') as f:
            f.write("# Excel Parser Benchmark Summary Report\n\n")
            f.write(f"Generated on: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # Overall statistics
            f.write("## Overall Statistics\n\n")
            total_tests = len(self.results)
            successful_tests = sum(1 for r in self.results if r.success)
            f.write(f"- Total tests run: {total_tests}\n")
            f.write(f"- Successful tests: {successful_tests}\n")
            f.write(f"- Success rate: {successful_tests/total_tests*100:.1f}%\n\n")

            # Parser comparison
            f.write("## Parser Performance Summary\n\n")
            parsers = set(r.parser_name for r in self.results)

            for parser in parsers:
                parser_results = [r for r in self.results if r.parser_name == parser and r.success]
                if not parser_results:
                    f.write(f"### {parser}\n")
                    f.write("No successful runs\n\n")
                    continue

                avg_time = sum(r.parsing_time_seconds for r in parser_results) / len(parser_results)
                avg_memory = sum(r.memory_peak_mb for r in parser_results) / len(parser_results)
                total_rows = sum(r.total_rows for r in parser_results)

                f.write(f"### {parser}\n")
                f.write(f"- Average parsing time: {avg_time:.2f} seconds\n")
                f.write(f"- Average peak memory: {avg_memory:.1f} MB\n")
                f.write(f"- Total rows processed: {total_rows:,}\n")
                f.write(f"- Success rate: {len(parser_results)}/{len([r for r in self.results if r.parser_name == parser])}\n\n")

            # File-specific results
            f.write("## File-Specific Results\n\n")
            files = set(r.file_name for r in self.results)

            for file_name in files:
                f.write(f"### {file_name}\n")
                file_results = [r for r in self.results if r.file_name == file_name]

                f.write("| Parser | Success | Time (s) | Memory (MB) | Rows | Columns |\n")
                f.write("|--------|---------|----------|-------------|------|----------|\n")

                for result in file_results:
                    success_icon = "✅" if result.success else "❌"
                    time_str = f"{result.parsing_time_seconds:.2f}" if result.success else "N/A"
                    memory_str = f"{result.memory_peak_mb:.1f}" if result.success else "N/A"
                    rows_str = str(result.total_rows) if result.success else "N/A"
                    cols_str = str(result.total_columns) if result.success else "N/A"

                    f.write(f"| {result.parser_name} | {success_icon} | {time_str} | {memory_str} | {rows_str} | {cols_str} |\n")

                f.write("\n")

    def _generate_detailed_analysis(self) -> None:
        """Generate detailed analysis report."""
        with open(self.output_dir / "detailed_analysis.md", 'w') as f:
            f.write("# Detailed Excel Parser Analysis\n\n")

            # Data integrity analysis
            f.write("## Data Integrity Analysis\n\n")

            for test_file in self.test_files:
                f.write(f"### {test_file.name}\n")
                f.write(f"**Expected:** {test_file.expected_rows} rows, {test_file.expected_columns} columns, {test_file.expected_sheets} sheets\n\n")

                file_results = [r for r in self.results if r.file_name == test_file.name and r.success]

                if not file_results:
                    f.write("No successful parsing results for this file.\n\n")
                    continue

                f.write("| Parser | Rows | Columns | Sheets | Data Types | Structured |\n")
                f.write("|--------|------|---------|--------|------------|------------|\n")

                for result in file_results:
                    data_types_str = ", ".join(f"{k}:{v}" for k, v in result.data_types_detected.items()) if result.data_types_detected else "N/A"
                    structured_icon = "✅" if result.structured_data_preserved else "❌"

                    f.write(f"| {result.parser_name} | {result.total_rows} | {result.total_columns} | {result.sheets_count} | {data_types_str} | {structured_icon} |\n")

                f.write("\n")

            # Feature support analysis
            f.write("## Feature Support Analysis\n\n")
            f.write("| Parser | Formulas | Multiple Sheets | Merged Cells | Formatting |\n")
            f.write("|--------|----------|-----------------|--------------|------------|\n")

            parsers = set(r.parser_name for r in self.results if r.success)
            for parser in parsers:
                parser_results = [r for r in self.results if r.parser_name == parser and r.success]
                if not parser_results:
                    continue

                # Take the most common values for feature support
                formulas = "✅" if any(r.formulas_handled for r in parser_results) else "❌"
                multi_sheets = "✅" if any(r.multiple_sheets_handled for r in parser_results) else "❌"
                merged_cells = "✅" if any(r.supports_merged_cells for r in parser_results) else "❌"
                formatting = "✅" if any(r.supports_formatting for r in parser_results) else "❌"

                f.write(f"| {parser} | {formulas} | {multi_sheets} | {merged_cells} | {formatting} |\n")

            f.write("\n")

    def _generate_performance_comparison(self) -> None:
        """Generate performance comparison charts and analysis."""
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns

            # Set style
            plt.style.use('default')
            sns.set_palette("husl")

            # Create performance comparison plots
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('Excel Parser Performance Comparison', fontsize=16)

            # Prepare data
            successful_results = [r for r in self.results if r.success]

            if not successful_results:
                self.logger.warning("No successful results to plot")
                return

            # 1. Parsing time comparison
            parsers = list(set(r.parser_name for r in successful_results))
            files = list(set(r.file_name for r in successful_results))

            time_data = []
            for parser in parsers:
                for file_name in files:
                    result = next((r for r in successful_results if r.parser_name == parser and r.file_name == file_name), None)
                    if result:
                        time_data.append({
                            'Parser': parser,
                            'File': file_name,
                            'Time': result.parsing_time_seconds
                        })

            if time_data:
                time_df = pd.DataFrame(time_data)
                sns.barplot(data=time_df, x='File', y='Time', hue='Parser', ax=axes[0, 0])
                axes[0, 0].set_title('Parsing Time Comparison')
                axes[0, 0].set_ylabel('Time (seconds)')
                axes[0, 0].tick_params(axis='x', rotation=45)

            # 2. Memory usage comparison
            memory_data = []
            for parser in parsers:
                for file_name in files:
                    result = next((r for r in successful_results if r.parser_name == parser and r.file_name == file_name), None)
                    if result:
                        memory_data.append({
                            'Parser': parser,
                            'File': file_name,
                            'Memory': result.memory_peak_mb
                        })

            if memory_data:
                memory_df = pd.DataFrame(memory_data)
                sns.barplot(data=memory_df, x='File', y='Memory', hue='Parser', ax=axes[0, 1])
                axes[0, 1].set_title('Peak Memory Usage Comparison')
                axes[0, 1].set_ylabel('Memory (MB)')
                axes[0, 1].tick_params(axis='x', rotation=45)

            # 3. Throughput (rows per second)
            throughput_data = []
            for result in successful_results:
                if result.parsing_time_seconds > 0 and result.total_rows > 0:
                    throughput = result.total_rows / result.parsing_time_seconds
                    throughput_data.append({
                        'Parser': result.parser_name,
                        'File': result.file_name,
                        'Throughput': throughput
                    })

            if throughput_data:
                throughput_df = pd.DataFrame(throughput_data)
                sns.barplot(data=throughput_df, x='File', y='Throughput', hue='Parser', ax=axes[1, 0])
                axes[1, 0].set_title('Throughput Comparison (Rows/Second)')
                axes[1, 0].set_ylabel('Rows per Second')
                axes[1, 0].tick_params(axis='x', rotation=45)

            # 4. File size vs parsing time scatter
            scatter_data = []
            for result in successful_results:
                scatter_data.append({
                    'Parser': result.parser_name,
                    'File Size (MB)': result.file_size_mb,
                    'Parsing Time (s)': result.parsing_time_seconds
                })

            if scatter_data:
                scatter_df = pd.DataFrame(scatter_data)
                for parser in parsers:
                    parser_data = scatter_df[scatter_df['Parser'] == parser]
                    axes[1, 1].scatter(parser_data['File Size (MB)'], parser_data['Parsing Time (s)'],
                                     label=parser, alpha=0.7, s=60)

                axes[1, 1].set_title('File Size vs Parsing Time')
                axes[1, 1].set_xlabel('File Size (MB)')
                axes[1, 1].set_ylabel('Parsing Time (seconds)')
                axes[1, 1].legend()

            plt.tight_layout()
            plt.savefig(self.output_dir / "performance_comparison.png", dpi=300, bbox_inches='tight')
            plt.close()

            self.logger.info("Performance comparison charts saved")

        except ImportError:
            self.logger.warning("matplotlib/seaborn not available. Skipping chart generation.")
        except Exception as e:
            self.logger.error(f"Error generating performance charts: {e}")


def main():
    """Main execution function."""
    import argparse

    parser = argparse.ArgumentParser(description="Excel Parser Benchmark Tool")
    parser.add_argument("--output-dir", type=Path, default=Path("benchmark_results"),
                       help="Output directory for results")
    parser.add_argument("--files", nargs="*",
                       help="Specific test files to run (default: all)")
    parser.add_argument("--install-docling", action="store_true",
                       help="Install Docling if not available")

    args = parser.parse_args()

    # Install Docling if requested
    if args.install_docling and not DOCLING_AVAILABLE:
        import subprocess
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "docling"])
            print("Docling installed successfully. Please restart the script.")
            return
        except subprocess.CalledProcessError as e:
            print(f"Failed to install Docling: {e}")
            return

    # Run benchmark
    benchmark = ExcelParserBenchmark(output_dir=args.output_dir)
    benchmark.run_benchmark(test_specific_files=args.files)

    print(f"\nBenchmark completed! Results saved to: {args.output_dir}")
    print(f"View the summary report: {args.output_dir / 'summary_report.md'}")


if __name__ == "__main__":
    main()
