# Excel Parser Benchmark

A comprehensive performance evaluation comparing RAGFlow's current Excel parsing implementation against IBM's Docling parser.

## Overview

This benchmark evaluates three Excel parsing approaches:

1. **RAGFlow Naive Chunking** - Current `RAGFlowExcelParser` with naive chunking method
2. **RAGFlow Table Chunking** - Specialized table chunking method from `rag/app/table.py`
3. **Docling Parser** - IBM's Docling document parsing library

## Evaluation Metrics

### Performance Metrics
- **Parsing Speed** - Time to process files
- **Memory Usage** - Peak memory consumption and delta
- **Throughput** - Rows processed per second
- **Scalability** - Performance with large files (>10MB)

### Data Integrity Metrics
- **Accuracy** - Correct extraction of rows, columns, sheets
- **Data Types** - Preservation of numeric, date, boolean types
- **Formulas** - Handling of Excel formulas
- **Multiple Sheets** - Support for multi-sheet workbooks

### Feature Completeness
- **Merged Cells** - Support for merged cell ranges
- **Formatting** - Preservation of cell formatting
- **Charts** - Handling of embedded charts
- **Complex Layouts** - Mixed content types

### Output Quality
- **Structured Data** - Preservation of tabular structure
- **Text Extraction** - Quality of extracted text content
- **Data Loss** - Completeness of extracted information

## Test Files

The benchmark generates diverse test files to evaluate different scenarios:

1. **simple_table.xlsx** - Clean tabular data (1,000 rows, 6 columns)
   - Ideal for Table chunking method
   - Tests basic data type detection

2. **complex_formatted.xlsx** - Mixed content with formatting
   - Report-style document with headers and tables
   - Tests handling of non-tabular content

3. **large_file.xlsx** - Scalability test (50,000 rows, 20 columns, >10MB)
   - Tests memory efficiency and processing speed
   - Evaluates performance with large datasets

4. **multi_sheet.xlsx** - Multiple sheets with formulas
   - Sales data, customer data, and summary sheets
   - Tests multi-sheet processing and formula handling

5. **formulas_datatypes.xlsx** - Various formulas and data types
   - Complex calculations and data type preservation
   - Tests formula evaluation and type detection

6. **merged_cells.xlsx** - Merged cells and complex formatting
   - Report layout with merged headers
   - Tests handling of complex Excel layouts

## Installation

### Prerequisites
- Python 3.10+
- RAGFlow codebase (this benchmark should be run from the RAGFlow root directory)

### Install Dependencies
```bash
cd benchmark
pip install -r requirements.txt
```

### Optional: Install Docling
```bash
pip install docling
```

## Usage

### Quick Start
```bash
cd benchmark
python run_benchmark.py
```

This will launch an interactive menu to choose between quick or full benchmark.

### Command Line Usage
```bash
# Run full benchmark
python excel_parser_benchmark.py

# Run specific files only
python excel_parser_benchmark.py --files simple_table.xlsx complex_formatted.xlsx

# Custom output directory
python excel_parser_benchmark.py --output-dir my_results

# Install Docling automatically
python excel_parser_benchmark.py --install-docling
```

### Quick Benchmark (Recommended for Testing)
Excludes large files, runs in ~5 minutes:
```bash
python run_benchmark.py
# Select option 1
```

### Full Benchmark
Includes all test files, runs in ~15-30 minutes:
```bash
python run_benchmark.py
# Select option 2
```

## Output

The benchmark generates comprehensive reports in the output directory:

### Files Generated
- `benchmark_results.json` - Raw benchmark data
- `summary_report.md` - Executive summary with key findings
- `detailed_analysis.md` - In-depth analysis of data integrity and features
- `performance_comparison.png` - Performance visualization charts
- `benchmark.log` - Detailed execution log
- `test_data/` - Generated test Excel files

### Sample Summary Report
```markdown
# Excel Parser Benchmark Summary Report

## Overall Statistics
- Total tests run: 18
- Successful tests: 16
- Success rate: 88.9%

## Parser Performance Summary

### RAGFlow_Naive
- Average parsing time: 0.45 seconds
- Average peak memory: 125.3 MB
- Total rows processed: 152,153
- Success rate: 6/6

### RAGFlow_Table
- Average parsing time: 0.62 seconds
- Average peak memory: 98.7 MB
- Total rows processed: 152,153
- Success rate: 6/6

### Docling
- Average parsing time: 1.23 seconds
- Average peak memory: 156.2 MB
- Total rows processed: 145,892
- Success rate: 4/6
```

## Key Findings & Recommendations

### When to Use Each Parser

#### RAGFlow Table Chunking
**Best for:**
- Clean tabular data with clear headers
- Data analysis scenarios requiring type preservation
- Large datasets where query performance matters
- Mixed data types (numbers, dates, text) in columns

**Advantages:**
- ✅ Superior data type detection and preservation
- ✅ Structured output ideal for downstream processing
- ✅ Efficient memory usage for large datasets
- ✅ Intelligent column mapping with field types

#### RAGFlow Naive Chunking
**Best for:**
- Free-form Excel content with mixed text and tables
- Documents with complex formatting that don't fit tabular structure
- Files where you want to preserve original text flow
- When you need the `html4excel` option for better formatting

**Advantages:**
- ✅ Handles any Excel content structure
- ✅ Faster processing for simple files
- ✅ Better compatibility with complex layouts
- ✅ Preserves text flow and context

#### Docling Parser
**Best for:**
- Document-centric workflows requiring rich formatting
- Integration with broader document processing pipelines
- When you need advanced layout understanding
- Multi-format document processing (not just Excel)

**Advantages:**
- ✅ Advanced document structure understanding
- ✅ Rich output format with metadata
- ✅ Handles complex layouts and merged cells well
- ✅ Part of comprehensive document processing ecosystem

### Performance Optimization Recommendations

1. **For Large Files (>10MB):**
   - Use RAGFlow Table chunking for structured data
   - Consider CSV preprocessing for maximum performance
   - Implement streaming processing for very large files

2. **For Mixed Content:**
   - Use RAGFlow Naive chunking with `html4excel=True`
   - Consider hybrid approach: detect content type first

3. **For Data Analysis:**
   - RAGFlow Table chunking provides best structured output
   - Leverage intelligent data type detection
   - Use field mapping for downstream processing

4. **Memory Optimization:**
   - RAGFlow Table chunking most memory-efficient
   - Process files in chunks for very large datasets
   - Clear intermediate objects between processing

## Integration with RAGFlow

### Backward Compatibility
All parsers maintain compatibility with existing RAGFlow chunking interfaces:
- Same callback mechanism for progress reporting
- Compatible output formats for downstream processing
- Existing parser configuration options preserved

### Recommended Implementation Strategy
1. **Phase 1:** Implement file size-based routing
   - Files >10MB → Table chunking
   - Complex layouts → Naive chunking
   - Simple tables → Table chunking

2. **Phase 2:** Add user configuration options
   - Allow users to choose parsing method
   - Provide intelligent defaults based on file analysis

3. **Phase 3:** Implement hybrid approach
   - Auto-detect content structure
   - Route to optimal parser automatically
   - Fallback mechanisms for edge cases

## Troubleshooting

### Common Issues

1. **Docling Import Error**
   ```bash
   pip install docling
   ```

2. **Memory Issues with Large Files**
   - Run quick benchmark first
   - Increase system memory
   - Process files individually

3. **Permission Errors**
   - Ensure write access to output directory
   - Run with appropriate permissions

### Performance Tips

1. **For faster benchmarks:**
   - Use quick benchmark mode
   - Test with smaller files first
   - Close other memory-intensive applications

2. **For accurate results:**
   - Run on dedicated system
   - Multiple benchmark runs for averaging
   - Monitor system resources during execution

## Contributing

To extend the benchmark:

1. **Add new test files:** Modify `_generate_test_files()` method
2. **Add new parsers:** Implement new `_benchmark_*()` method
3. **Add new metrics:** Extend `BenchmarkResult` dataclass
4. **Improve reports:** Enhance `_generate_*_report()` methods

## License

This benchmark tool is part of the RAGFlow project and follows the same Apache 2.0 license.
