# Detailed Excel Parser Analysis

## Data Integrity Analysis

### simple_table.xlsx
**Expected:** 1001 rows, 6 columns, 1 sheets

| Parser | Rows | Columns | Sheets | Data Types | Structured |
|--------|------|---------|--------|------------|------------|
| RAGFlow_Naive | 1000 | 0 | 1 | N/A | ❌ |
| RAGFlow_Table | 1000 | 6 | 1 | object:6 | ✅ |
| Docling | 0 | 0 | 1 | N/A | ✅ |

### complex_formatted.xlsx
**Expected:** 13 rows, 4 columns, 1 sheets

| Parser | Rows | Columns | Sheets | Data Types | Structured |
|--------|------|---------|--------|------------|------------|
| RAGFlow_Naive | 12 | 0 | 1 | N/A | ❌ |
| RAGFlow_Table | 12 | 1 | 1 | object:1 | ✅ |
| Docling | 0 | 0 | 1 | N/A | ✅ |

### large_file.xlsx
**Expected:** 50001 rows, 20 columns, 1 sheets

| Parser | Rows | Columns | Sheets | Data Types | Structured |
|--------|------|---------|--------|------------|------------|
| RAGFlow_Naive | 50000 | 0 | 1 | N/A | ❌ |
| RAGFlow_Table | 50000 | 20 | 1 | object:20 | ✅ |
| Docling | 0 | 0 | 1 | N/A | ✅ |

### multi_sheet.xlsx
**Expected:** 153 rows, 5 columns, 3 sheets

| Parser | Rows | Columns | Sheets | Data Types | Structured |
|--------|------|---------|--------|------------|------------|
| RAGFlow_Naive | 153 | 0 | 1 | N/A | ❌ |
| RAGFlow_Table | 153 | 5 | 3 | object:11 | ✅ |
| Docling | 0 | 0 | 1 | N/A | ✅ |

### formulas_datatypes.xlsx
**Expected:** 52 rows, 9 columns, 1 sheets

| Parser | Rows | Columns | Sheets | Data Types | Structured |
|--------|------|---------|--------|------------|------------|
| RAGFlow_Naive | 51 | 0 | 1 | N/A | ❌ |
| RAGFlow_Table | 51 | 9 | 1 | object:9 | ✅ |
| Docling | 0 | 0 | 1 | N/A | ✅ |

### merged_cells.xlsx
**Expected:** 11 rows, 5 columns, 1 sheets

| Parser | Rows | Columns | Sheets | Data Types | Structured |
|--------|------|---------|--------|------------|------------|
| RAGFlow_Naive | 10 | 0 | 1 | N/A | ❌ |
| RAGFlow_Table | 10 | 1 | 1 | object:1 | ✅ |
| Docling | 0 | 0 | 1 | N/A | ✅ |

## Feature Support Analysis

| Parser | Formulas | Multiple Sheets | Merged Cells | Formatting |
|--------|----------|-----------------|--------------|------------|
| RAGFlow_Table | ✅ | ✅ | ❌ | ❌ |
| RAGFlow_Naive | ✅ | ✅ | ❌ | ❌ |
| Docling | ✅ | ❌ | ✅ | ✅ |

