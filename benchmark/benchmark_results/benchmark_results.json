[{"parser_name": "RAGFlow_Naive", "file_name": "simple_table.xlsx", "file_size_mb": 0.03455352783203125, "parsing_time_seconds": 1.1121985912322998, "memory_peak_mb": 2.7119150161743164, "memory_delta_mb": 0.51171875, "success": true, "error_message": null, "total_rows": 1000, "total_columns": 0, "sheets_count": 1, "data_types_detected": {}, "output_size_chars": 102787, "structured_data_preserved": false, "formulas_handled": true, "multiple_sheets_handled": true, "supports_merged_cells": false, "supports_formatting": false, "supports_charts": false}, {"parser_name": "RAGFlow_Table", "file_name": "simple_table.xlsx", "file_size_mb": 0.03455352783203125, "parsing_time_seconds": 0.714165210723877, "memory_peak_mb": 3.463613510131836, "memory_delta_mb": 0.0, "success": true, "error_message": null, "total_rows": 1000, "total_columns": 6, "sheets_count": 1, "data_types_detected": {"object": 6}, "output_size_chars": 382915, "structured_data_preserved": true, "formulas_handled": true, "multiple_sheets_handled": true, "supports_merged_cells": false, "supports_formatting": false, "supports_charts": false}, {"parser_name": "<PERSON><PERSON>", "file_name": "simple_table.xlsx", "file_size_mb": 0.03455352783203125, "parsing_time_seconds": 0.9635109901428223, "memory_peak_mb": 17.283063888549805, "memory_delta_mb": 7.33203125, "success": true, "error_message": null, "total_rows": 0, "total_columns": 0, "sheets_count": 1, "data_types_detected": {}, "output_size_chars": 76151, "structured_data_preserved": true, "formulas_handled": true, "multiple_sheets_handled": false, "supports_merged_cells": true, "supports_formatting": true, "supports_charts": false}, {"parser_name": "RAGFlow_Naive", "file_name": "complex_formatted.xlsx", "file_size_mb": 0.0049076080322265625, "parsing_time_seconds": 0.5248682498931885, "memory_peak_mb": 0.13312721252441406, "memory_delta_mb": 0.0, "success": true, "error_message": null, "total_rows": 12, "total_columns": 0, "sheets_count": 1, "data_types_detected": {}, "output_size_chars": 651, "structured_data_preserved": false, "formulas_handled": true, "multiple_sheets_handled": true, "supports_merged_cells": false, "supports_formatting": false, "supports_charts": false}, {"parser_name": "RAGFlow_Table", "file_name": "complex_formatted.xlsx", "file_size_mb": 0.0049076080322265625, "parsing_time_seconds": 0.4250028133392334, "memory_peak_mb": 0.1556711196899414, "memory_delta_mb": 0.0, "success": true, "error_message": null, "total_rows": 12, "total_columns": 1, "sheets_count": 1, "data_types_detected": {"object": 1}, "output_size_chars": 868, "structured_data_preserved": true, "formulas_handled": true, "multiple_sheets_handled": true, "supports_merged_cells": false, "supports_formatting": false, "supports_charts": false}, {"parser_name": "<PERSON><PERSON>", "file_name": "complex_formatted.xlsx", "file_size_mb": 0.0049076080322265625, "parsing_time_seconds": 0.4500091075897217, "memory_peak_mb": 0.2341775894165039, "memory_delta_mb": 0.0, "success": true, "error_message": null, "total_rows": 0, "total_columns": 0, "sheets_count": 1, "data_types_detected": {}, "output_size_chars": 605, "structured_data_preserved": true, "formulas_handled": true, "multiple_sheets_handled": false, "supports_merged_cells": true, "supports_formatting": true, "supports_charts": false}, {"parser_name": "RAGFlow_Naive", "file_name": "large_file.xlsx", "file_size_mb": 4.103572845458984, "parsing_time_seconds": 52.99691843986511, "memory_peak_mb": 414.02357482910156, "memory_delta_mb": 1007.3828125, "success": true, "error_message": null, "total_rows": 50000, "total_columns": 0, "sheets_count": 1, "data_types_detected": {}, "output_size_chars": 18246698, "structured_data_preserved": false, "formulas_handled": true, "multiple_sheets_handled": true, "supports_merged_cells": false, "supports_formatting": false, "supports_charts": false}, {"parser_name": "RAGFlow_Table", "file_name": "large_file.xlsx", "file_size_mb": 4.103572845458984, "parsing_time_seconds": 57.270487785339355, "memory_peak_mb": 571.3068141937256, "memory_delta_mb": 630.56640625, "success": true, "error_message": null, "total_rows": 50000, "total_columns": 20, "sheets_count": 1, "data_types_detected": {"object": 20}, "output_size_chars": 65121826, "structured_data_preserved": true, "formulas_handled": true, "multiple_sheets_handled": true, "supports_merged_cells": false, "supports_formatting": false, "supports_charts": false}, {"parser_name": "<PERSON><PERSON>", "file_name": "large_file.xlsx", "file_size_mb": 4.103572845458984, "parsing_time_seconds": 111.69170355796814, "memory_peak_mb": 2804.438543319702, "memory_delta_mb": 4161.046875, "success": true, "error_message": null, "total_rows": 0, "total_columns": 0, "sheets_count": 1, "data_types_detected": {}, "output_size_chars": 14150565, "structured_data_preserved": true, "formulas_handled": true, "multiple_sheets_handled": false, "supports_merged_cells": true, "supports_formatting": true, "supports_charts": false}, {"parser_name": "RAGFlow_Naive", "file_name": "multi_sheet.xlsx", "file_size_mb": 0.010237693786621094, "parsing_time_seconds": 0.5924651622772217, "memory_peak_mb": 0.5539779663085938, "memory_delta_mb": 0.0, "success": true, "error_message": null, "total_rows": 153, "total_columns": 0, "sheets_count": 1, "data_types_detected": {}, "output_size_chars": 14023, "structured_data_preserved": false, "formulas_handled": true, "multiple_sheets_handled": true, "supports_merged_cells": false, "supports_formatting": false, "supports_charts": false}, {"parser_name": "RAGFlow_Table", "file_name": "multi_sheet.xlsx", "file_size_mb": 0.010237693786621094, "parsing_time_seconds": 0.6027815341949463, "memory_peak_mb": 0.5609416961669922, "memory_delta_mb": 0.0, "success": true, "error_message": null, "total_rows": 153, "total_columns": 5, "sheets_count": 3, "data_types_detected": {"object": 11}, "output_size_chars": 40380, "structured_data_preserved": true, "formulas_handled": true, "multiple_sheets_handled": true, "supports_merged_cells": false, "supports_formatting": false, "supports_charts": false}, {"parser_name": "<PERSON><PERSON>", "file_name": "multi_sheet.xlsx", "file_size_mb": 0.010237693786621094, "parsing_time_seconds": 0.5190105438232422, "memory_peak_mb": 1.7824773788452148, "memory_delta_mb": 0.0, "success": true, "error_message": null, "total_rows": 0, "total_columns": 0, "sheets_count": 1, "data_types_detected": {}, "output_size_chars": 11333, "structured_data_preserved": true, "formulas_handled": true, "multiple_sheets_handled": false, "supports_merged_cells": true, "supports_formatting": true, "supports_charts": false}, {"parser_name": "RAGFlow_Naive", "file_name": "formulas_datatypes.xlsx", "file_size_mb": 0.007357597351074219, "parsing_time_seconds": 0.43882131576538086, "memory_peak_mb": 0.46654605865478516, "memory_delta_mb": 0.0, "success": true, "error_message": null, "total_rows": 51, "total_columns": 0, "sheets_count": 1, "data_types_detected": {}, "output_size_chars": 5087, "structured_data_preserved": false, "formulas_handled": true, "multiple_sheets_handled": true, "supports_merged_cells": false, "supports_formatting": false, "supports_charts": false}, {"parser_name": "RAGFlow_Table", "file_name": "formulas_datatypes.xlsx", "file_size_mb": 0.007357597351074219, "parsing_time_seconds": 0.4397096633911133, "memory_peak_mb": 0.4838123321533203, "memory_delta_mb": 0.0, "success": true, "error_message": null, "total_rows": 51, "total_columns": 9, "sheets_count": 1, "data_types_detected": {"object": 9}, "output_size_chars": 17275, "structured_data_preserved": true, "formulas_handled": true, "multiple_sheets_handled": true, "supports_merged_cells": false, "supports_formatting": false, "supports_charts": false}, {"parser_name": "<PERSON><PERSON>", "file_name": "formulas_datatypes.xlsx", "file_size_mb": 0.007357597351074219, "parsing_time_seconds": 0.48987698554992676, "memory_peak_mb": 1.4496746063232422, "memory_delta_mb": 0.0, "success": true, "error_message": null, "total_rows": 0, "total_columns": 0, "sheets_count": 1, "data_types_detected": {}, "output_size_chars": 6624, "structured_data_preserved": true, "formulas_handled": true, "multiple_sheets_handled": false, "supports_merged_cells": true, "supports_formatting": true, "supports_charts": false}, {"parser_name": "RAGFlow_Naive", "file_name": "merged_cells.xlsx", "file_size_mb": 0.005013465881347656, "parsing_time_seconds": 0.4171638488769531, "memory_peak_mb": 0.14339828491210938, "memory_delta_mb": 0.0, "success": true, "error_message": null, "total_rows": 10, "total_columns": 0, "sheets_count": 1, "data_types_detected": {}, "output_size_chars": 591, "structured_data_preserved": false, "formulas_handled": true, "multiple_sheets_handled": true, "supports_merged_cells": false, "supports_formatting": false, "supports_charts": false}, {"parser_name": "RAGFlow_Table", "file_name": "merged_cells.xlsx", "file_size_mb": 0.005013465881347656, "parsing_time_seconds": 0.43169593811035156, "memory_peak_mb": 0.1562480926513672, "memory_delta_mb": 0.0, "success": true, "error_message": null, "total_rows": 10, "total_columns": 1, "sheets_count": 1, "data_types_detected": {"object": 1}, "output_size_chars": 695, "structured_data_preserved": true, "formulas_handled": true, "multiple_sheets_handled": true, "supports_merged_cells": false, "supports_formatting": false, "supports_charts": false}, {"parser_name": "<PERSON><PERSON>", "file_name": "merged_cells.xlsx", "file_size_mb": 0.005013465881347656, "parsing_time_seconds": 0.4467649459838867, "memory_peak_mb": 0.2600822448730469, "memory_delta_mb": 0.0, "success": true, "error_message": null, "total_rows": 0, "total_columns": 0, "sheets_count": 1, "data_types_detected": {}, "output_size_chars": 1248, "structured_data_preserved": true, "formulas_handled": true, "multiple_sheets_handled": false, "supports_merged_cells": true, "supports_formatting": true, "supports_charts": false}]