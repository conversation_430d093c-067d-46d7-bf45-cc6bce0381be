[
  {
    "parser_name": "RAGFlow_Naive",
    "file_name": "simple_table.xlsx",
    "file_size_mb": 0.034552574157714844,
    "parsing_time_seconds": 1.1884942054748535,
    "memory_peak_mb": 2.712183952331543,
    "memory_delta_mb": 0.0,
    "success": true,
    "error_message": null,
    "total_rows": 1000,
    "total_columns": 0,
    "sheets_count": 1,
    "data_types_detected": {},
    "output_size_chars": 102787,
    "structured_data_preserved": false,
    "formulas_handled": true,
    "multiple_sheets_handled": true,
    "supports_merged_cells": false,
    "supports_formatting": false,
    "supports_charts": false
  },
  {
    "parser_name": "RAGFlow_Table",
    "file_name": "simple_table.xlsx",
    "file_size_mb": 0.034552574157714844,
    "parsing_time_seconds": 0.8139824867248535,
    "memory_peak_mb": 3.463517189025879,
    "memory_delta_mb": 0.0,
    "success": true,
    "error_message": null,
    "total_rows": 1000,
    "total_columns": 6,
    "sheets_count": 1,
    "data_types_detected": {
      "object": 6
    },
    "output_size_chars": 